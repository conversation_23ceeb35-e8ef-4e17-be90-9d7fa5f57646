import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score, RepeatedStratifiedKFold
from sklearn.preprocessing import StandardScaler
from sklearn.naive_bayes import GaussianNB
from sklearn.tree import DecisionTreeClassifier
from sklearn.metrics import classification_report, confusion_matrix, roc_curve, auc, accuracy_score, precision_score, recall_score, f1_score
from imblearn.over_sampling import SMOTE
import matplotlib.pyplot as plt
import seaborn as sns
import os
import warnings
import time
import matplotlib

warnings.filterwarnings('ignore')
matplotlib.rcParams['font.family'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

# 读取数据
df = pd.read_csv('event.csv', encoding='GB18030')
print("原始数据形状：", df.shape)

# 删除包含 IP、时间 的列
df = df.loc[:, ~df.columns.str.contains('IP')]
df = df.loc[:, ~df.columns.str.contains('时间')]

# 添加标签列
def multi_class_label(row):
    if isinstance(row['事件类型'], str) and ('攻击' in row['事件类型'] or '刺探' in row['事件类型'] or '漏洞' in row['事件类型']):
        return 1
    if row['事件等级'] in ['严重', '重要']:
        return 1
    if row['结果'] in ['成功', '失败']:
        return 1
    return 0

df['is_anomaly'] = df.apply(multi_class_label, axis=1)

# 划分特征与标签
drop_columns = ['is_anomaly', '事件类型', '事件等级', '结果']
X = df.drop(columns=drop_columns)
y = df['is_anomaly']

# 缺失值处理
missing_percent = X.isnull().mean()
X = X.drop(columns=missing_percent[missing_percent > 0.3].index)

# 数值列填充
numeric_cols = X.select_dtypes(include=['number']).columns
for col in numeric_cols:
    mean_val = X[col].mean()
    X[col].fillna(mean_val, inplace=True)

# 非数值列填充
categorical_cols = X.select_dtypes(exclude=['number']).columns
for col in categorical_cols:
    X[col].fillna('UNKNOWN', inplace=True)

# One-hot 编码
X_encoded = pd.get_dummies(X, drop_first=True)

# 时间排序 + 时间顺序划分训练测试集（假设数据原有顺序代表时间）
split_idx = int(len(X_encoded) * 0.67)
X_train_raw = X_encoded.iloc[:split_idx]
X_test_raw = X_encoded.iloc[split_idx:]
y_train = y.iloc[:split_idx]
y_test = y.iloc[split_idx:]

# 使用 SMOTE 平衡训练集
smote = SMOTE(random_state=42)
X_train, y_train = smote.fit_resample(X_train_raw, y_train)

# 模型训练与评估
models = {
    'Naive Bayes': GaussianNB(),
    'Decision Tree': DecisionTreeClassifier(max_depth=3, min_samples_leaf=5, random_state=42)
}

results = {}
cv = RepeatedStratifiedKFold(n_splits=2, n_repeats=5, random_state=42)

for name, model in models.items():
    print(f"\n训练模型：{name}")
    t0 = time.time()
    model.fit(X_train, y_train)
    t1 = time.time()

    y_proba = model.predict_proba(X_test_raw)[:, 1] if hasattr(model, 'predict_proba') else None
    y_pred = model.predict(X_test_raw)
    t2 = time.time()

    scores = cross_val_score(model, X_train, y_train, cv=cv, scoring='f1')

    results[name] = {
        'f1_cv': np.mean(scores),
        'accuracy': accuracy_score(y_test, y_pred),
        'precision': precision_score(y_test, y_pred),
        'recall': recall_score(y_test, y_pred),
        'f1': f1_score(y_test, y_pred),
        'auc': auc(*roc_curve(y_test, y_proba)[:2]) if y_proba is not None else None,
        'y_pred': y_pred,
        'y_proba': y_proba,
        'train_time': t1 - t0,
        'test_time': t2 - t1
    }

    print(f"{name} 评估结果：F1(CV)={results[name]['f1_cv']:.4f}, 准确率={results[name]['accuracy']:.4f}, F1={results[name]['f1']:.4f}")

# ROC 可视化
plt.figure(figsize=(8, 6))
for name, res in results.items():
    if res['y_proba'] is not None:
        fpr, tpr, _ = roc_curve(y_test, res['y_proba'])
        plt.plot(fpr, tpr, label=f"{name} (AUC={res['auc']:.2f})")
plt.plot([0, 1], [0, 1], 'k--')
plt.xlabel('假阳性率')
plt.ylabel('真正率')
plt.title('ROC 曲线')
plt.legend()
plt.tight_layout()
os.makedirs('output', exist_ok=True)
plt.savefig('output/roc_curve.png')

# 混淆矩阵
best_model_name = max(results, key=lambda x: results[x]['f1'])
cm = confusion_matrix(y_test, results[best_model_name]['y_pred'])
plt.figure(figsize=(6, 4))
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
plt.title(f'最佳模型混淆矩阵：{best_model_name}')
plt.xlabel('预测值')
plt.ylabel('真实值')
plt.tight_layout()
plt.savefig('output/confusion_matrix.png')

# 保存结果
df.to_csv('output/cleaned_event.csv', index=False, encoding='GB18030')
pd.DataFrame([{
    '模型': name,
    'F1（CV）': res['f1_cv'],
    '准确率': res['accuracy'],
    '精确率': res['precision'],
    '召回率': res['recall'],
    'F1': res['f1'],
    'AUC': res['auc'],
    '训练时间': res['train_time'],
    '测试时间': res['test_time']
} for name, res in results.items()]).to_csv('output/model_metrics.csv', index=False, encoding='GB18030')

print("\n✅ 实验完成，所有结果已保存到 output 文件夹。")
