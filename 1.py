import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import joblib
import os
import time
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.model_selection import train_test_split, cross_val_score, RepeatedStratifiedKFold
from sklearn.naive_bayes import GaussianNB
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import (classification_report, confusion_matrix, roc_curve, 
                            auc, accuracy_score, precision_score, recall_score, f1_score, roc_auc_score)
import matplotlib
import warnings

# 初始化设置
warnings.filterwarnings('ignore')
matplotlib.rcParams['font.family'] = ['SimHei']  # 中文显示
matplotlib.rcParams['axes.unicode_minus'] = False  # 负号显示
os.makedirs('output', exist_ok=True)

class NetworkAnomalyDetection:
    def __init__(self, file_path):
        self.file_path = file_path
        self.label_encoders = {}
        self.models = {
            'Naive Bayes': GaussianNB(),
            'Decision Tree': DecisionTreeClassifier(max_depth=3, min_samples_leaf=5, random_state=42),
            'Random Forest': RandomForestClassifier(max_depth=10, min_samples_split=10, random_state=42)
        }
        self.results = {}
        
    def load_data(self):
        """加载并预处理数据"""
        print("\n>>> 步骤1：加载数据")
        self.df = pd.read_csv(self.file_path, encoding='GB18030')
        print(f"原始数据形状: {self.df.shape}")
        
        # 删除不需要的列
        self.df = self.df.loc[:, ~self.df.columns.str.contains('IP|时间|端口')]
        
        # 添加标签列
        self._add_label_column()
        print(f"处理后数据形状: {self.df.shape}")
        
    def _add_label_column(self):
        """创建异常检测标签"""
        print("\n>>> 步骤2：创建标签列")
        
        def label_logic(row):
            # 事件类型判断
            if isinstance(row['事件类型'], str) and any(x in row['事件类型'] for x in ['攻击', '刺探', '漏洞']):
                return 1
            # 事件等级判断
            if row['事件等级'] in ['严重', '重要']:
                return 1
            # 结果判断
            if row['结果'] in ['成功', '失败']:
                return 1
            return 0
        
        self.df['is_anomaly'] = self.df.apply(label_logic, axis=1)
        print(f"异常样本比例: {self.df['is_anomaly'].mean():.2%}")
        
    def preprocess_data(self):
        """数据预处理"""
        print("\n>>> 步骤3：数据预处理")
        # 删除高度相关列
        drop_cols = ['is_anomaly', '事件类型', '事件等级', '结果']
        self.X = self.df.drop(columns=drop_cols)
        self.y = self.df['is_anomaly']
        
        # 划分训练测试集
        self.X_train_raw, self.X_test_raw, self.y_train, self.y_test = train_test_split(
            self.X, self.y, test_size=1/3, stratify=self.y, random_state=42
        )
        
        # 处理缺失值
        self._handle_missing_values()
        
    def _handle_missing_values(self):
        """处理缺失值"""
        print("\n>>> 步骤4：处理缺失值")
        # 删除高缺失率列
        missing_rate = self.X_train_raw.isnull().mean()
        drop_cols = missing_rate[missing_rate > 0.3].index
        self.X_train_raw = self.X_train_raw.drop(columns=drop_cols)
        self.X_test_raw = self.X_test_raw.drop(columns=drop_cols)
        
        # 填充数值列
        num_cols = self.X_train_raw.select_dtypes(include='number').columns
        for col in num_cols:
            train_mean = self.X_train_raw[col].mean()
            self.X_train_raw[col].fillna(train_mean, inplace=True)
            self.X_test_raw[col].fillna(train_mean, inplace=True)
        
        # 填充非数值列
        cat_cols = self.X_train_raw.select_dtypes(exclude='number').columns
        for col in cat_cols:
            self.X_train_raw[col].fillna('UNKNOWN', inplace=True)
            self.X_test_raw[col].fillna('UNKNOWN', inplace=True)
        
        print(f"训练集形状: {self.X_train_raw.shape}, 测试集形状: {self.X_test_raw.shape}")
        
    def feature_encoding(self):
        """特征编码"""
        print("\n>>> 步骤5：特征编码")
        self.X_train = self.X_train_raw.copy()
        self.X_test = self.X_test_raw.copy()
        
        # 分类特征编码
        cat_cols = self.X_train.select_dtypes(include='object').columns
        for col in cat_cols:
            le = LabelEncoder()
            # 合并训练测试集避免未知标签
            combined = pd.concat([self.X_train[col], self.X_test[col]]).astype(str)
            le.fit(combined)
            
            self.X_train[col] = le.transform(self.X_train[col].astype(str))
            self.X_test[col] = le.transform(self.X_test[col].astype(str))
            self.label_encoders[col] = le
        
        print(f"编码后特征数: {self.X_train.shape[1]}")
        
    def train_models(self):
        """训练并评估模型"""
        print("\n>>> 步骤6：模型训练与评估")
        cv = RepeatedStratifiedKFold(n_splits=3, n_repeats=2, random_state=42)
        
        for name, model in self.models.items():
            print(f"\n--- 正在训练 {name} ---")
            start_time = time.time()
            
            # 交叉验证
            cv_scores = cross_val_score(model, self.X_train, self.y_train, cv=cv, scoring='f1')
            
            # 训练模型
            model.fit(self.X_train, self.y_train)
            train_time = time.time() - start_time
            
            # 预测
            y_pred = model.predict(self.X_test)
            y_proba = model.predict_proba(self.X_test)[:, 1] if hasattr(model, 'predict_proba') else None
            
            # 计算指标
            metrics = {
                'model': model,
                'f1_cv': np.mean(cv_scores),
                'accuracy': accuracy_score(self.y_test, y_pred),
                'precision': precision_score(self.y_test, y_pred),
                'recall': recall_score(self.y_test, y_pred),
                'f1': f1_score(self.y_test, y_pred),
                'auc': roc_auc_score(self.y_test, y_proba) if y_proba is not None else None,
                'train_time': train_time,
                'y_pred': y_pred,
                'y_proba': y_proba
            }
            
            self.results[name] = metrics
            self._print_metrics(name, metrics)
            
    def _print_metrics(self, name, metrics):
        """打印模型评估指标"""
        print(f"\n{name} 性能指标:")
        print(f"- F1 (交叉验证): {metrics['f1_cv']:.4f}")
        print(f"- 准确率: {metrics['accuracy']:.4f}")
        print(f"- 精确率: {metrics['precision']:.4f}")
        print(f"- 召回率: {metrics['recall']:.4f}")
        print(f"- F1 (测试集): {metrics['f1']:.4f}")
        if metrics['auc'] is not None:
            print(f"- AUC: {metrics['auc']:.4f}")
        print(f"- 训练时间: {metrics['train_time']:.2f}s")
        
    def visualize_results(self):
        """可视化评估结果"""
        print("\n>>> 步骤7：结果可视化")
        
        # ROC曲线
        plt.figure(figsize=(10, 6))
        for name, res in self.results.items():
            if res['y_proba'] is not None:
                fpr, tpr, _ = roc_curve(self.y_test, res['y_proba'])
                plt.plot(fpr, tpr, label=f"{name} (AUC={res['auc']:.2f})")
        
        plt.plot([0, 1], [0, 1], 'k--')
        plt.xlabel('假阳性率')
        plt.ylabel('真正率')
        plt.title('模型ROC曲线比较')
        plt.legend()
        plt.grid()
        plt.savefig('output/roc_curves.png')
        plt.close()
        
        # 混淆矩阵
        best_model = max(self.results.items(), key=lambda x: x[1]['f1'])
        cm = confusion_matrix(self.y_test, best_model[1]['y_pred'])
        
        plt.figure(figsize=(6, 4))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
        plt.title(f'最佳模型混淆矩阵: {best_model[0]}')
        plt.xlabel('预测标签')
        plt.ylabel('真实标签')
        plt.savefig('output/confusion_matrix.png')
        plt.close()
        
        # 特征重要性
        if hasattr(best_model[1]['model'], 'feature_importances_'):
            importances = best_model[1]['model'].feature_importances_
            indices = np.argsort(importances)[::-1]
            
            plt.figure(figsize=(12, 6))
            sns.barplot(x=importances[indices][:20], y=self.X_train.columns[indices][:20])
            plt.title('Top 20 特征重要性')
            plt.tight_layout()
            plt.savefig('output/feature_importance.png')
            plt.close()
            
    def save_results(self):
        """保存结果"""
        print("\n>>> 步骤8：保存结果")
        
        # 保存模型
        joblib.dump(self.results, 'output/models.pkl')
        
        # 保存评估指标
        metrics_df = pd.DataFrame([{
            '模型': name,
            'F1_CV': res['f1_cv'],
            '准确率': res['accuracy'],
            '精确率': res['precision'],
            '召回率': res['recall'],
            'F1': res['f1'],
            'AUC': res['auc'],
            '训练时间(s)': res['train_time']
        } for name, res in self.results.items()])
        
        metrics_df.to_csv('output/model_metrics.csv', index=False, encoding='GB18030')
        
        # 保存分类报告
        best_model = max(self.results.items(), key=lambda x: x[1]['f1'])
        report = classification_report(self.y_test, best_model[1]['y_pred'], output_dict=True)
        pd.DataFrame(report).transpose().to_csv('output/classification_report.csv', encoding='GB18030')
        
        print("✅ 所有结果已保存到output文件夹")

if __name__ == "__main__":
    # 初始化并运行实验
    detector = NetworkAnomalyDetection('event.csv')
    detector.load_data()
    detector.preprocess_data()
    detector.feature_encoding()
    detector.train_models()
    detector.visualize_results()
    detector.save_results()