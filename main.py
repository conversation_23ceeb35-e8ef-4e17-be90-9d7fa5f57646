import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestClassifier
from sklearn.tree import DecisionTreeClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.neural_network import MLPClassifier
from sklearn.metrics import (accuracy_score, precision_score, recall_score,
                           roc_auc_score, roc_curve, confusion_matrix,
                           classification_report)
import matplotlib.pyplot as plt
import seaborn as sns
import os
from datetime import datetime
import warnings
import pickle
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def create_output_dir():
    """创建输出目录"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"output/{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

def load_and_preprocess_data():
    """加载和预处理数据"""
    print("正在加载数据...")

    # 读取数据
    df = pd.read_csv('event.csv', encoding='gb18030')
    print(f"数据集形状: {df.shape}")

    # 创建二分类标签：严重和重要为异常(1)，其他为正常(0)
    df['label'] = df['事件等级'].apply(lambda x: 1 if x in ['严重', '重要'] else 0)
    print(f"异常样本数: {df['label'].sum()}, 正常样本数: {len(df) - df['label'].sum()}")

    # 选择数值特征
    numeric_features = ['源端口', '目的端口', '持续时间', '发送流量', '接收流量',
                       '事件归并数目', '监控数值', '备用整形2', '备用长整形1',
                       '备用长整形2', '备用双精度1', '备用双精度2']

    # 选择分类特征
    categorical_features = ['事件类型', '设备类型', '网络协议', '网络应用协议',
                          '操作', '结果', '日志类型']

    # 处理数值特征
    for col in numeric_features:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
            df[col].fillna(df[col].median(), inplace=True)

    # 处理分类特征
    le_dict = {}
    for col in categorical_features:
        if col in df.columns:
            le = LabelEncoder()
            df[col] = df[col].astype(str).fillna('unknown')
            df[col + '_encoded'] = le.fit_transform(df[col])
            le_dict[col] = le

    # 选择最终特征
    feature_columns = []
    for col in numeric_features:
        if col in df.columns:
            feature_columns.append(col)

    for col in categorical_features:
        if col in df.columns:
            feature_columns.append(col + '_encoded')

    X = df[feature_columns].fillna(0)
    y = df['label']

    print(f"特征数量: {X.shape[1]}")
    print(f"使用的特征: {feature_columns}")

    return X, y, df, feature_columns, le_dict

def perform_cross_validation(X, y, models, output_dir):
    """执行10折交叉验证，重复10轮"""
    print("开始10折交叉验证（10轮重复）...")

    results = {name: {'accuracy': [], 'precision': [], 'recall': [], 'auc': []}
               for name in models.keys()}

    # 10轮重复
    for round_num in range(10):
        print(f"第 {round_num + 1} 轮交叉验证...")

        # 10折交叉验证
        skf = StratifiedKFold(n_splits=10, shuffle=True, random_state=round_num)

        for name, model in models.items():
            fold_scores = {'accuracy': [], 'precision': [], 'recall': [], 'auc': []}

            for fold_idx, (train_idx, val_idx) in enumerate(skf.split(X, y)):
                X_train_fold, X_val_fold = X.iloc[train_idx], X.iloc[val_idx]
                y_train_fold, y_val_fold = y.iloc[train_idx], y.iloc[val_idx]

                # 标准化
                scaler = StandardScaler()
                X_train_scaled = scaler.fit_transform(X_train_fold)
                X_val_scaled = scaler.transform(X_val_fold)

                # 训练模型
                model.fit(X_train_scaled, y_train_fold)

                # 预测
                y_pred = model.predict(X_val_scaled)
                y_pred_proba = (model.predict_proba(X_val_scaled)[:, 1]
                              if hasattr(model, 'predict_proba') else y_pred)

                # 计算指标
                fold_scores['accuracy'].append(accuracy_score(y_val_fold, y_pred))
                fold_scores['precision'].append(precision_score(y_val_fold, y_pred,
                                                              average='binary', zero_division=0))
                fold_scores['recall'].append(recall_score(y_val_fold, y_pred,
                                                        average='binary', zero_division=0))
                fold_scores['auc'].append(roc_auc_score(y_val_fold, y_pred_proba))

            # 保存本轮平均结果
            for metric in fold_scores:
                results[name][metric].append(np.mean(fold_scores[metric]))

    # 计算最终统计结果
    cv_results = {}
    for name in models.keys():
        cv_results[name] = {}
        for metric in results[name]:
            scores = results[name][metric]
            cv_results[name][metric] = {
                'mean': np.mean(scores),
                'std': np.std(scores),
                'scores': scores
            }

    # 保存交叉验证结果
    cv_df = pd.DataFrame({
        name: [f"{cv_results[name][metric]['mean']:.4f}±{cv_results[name][metric]['std']:.4f}"
               for metric in ['accuracy', 'precision', 'recall', 'auc']]
        for name in models.keys()
    }, index=['Accuracy', 'Precision', 'Recall', 'AUC'])

    cv_df.to_csv(f"{output_dir}/cross_validation_results.csv")
    print("交叉验证完成！")

    return cv_results

def train_and_evaluate_models(X, y, output_dir):
    """训练和评估模型"""
    print("开始训练和评估模型...")

    # 划分训练集和测试集 (2/3 训练, 1/3 测试)
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=1/3,
                                                        random_state=42, stratify=y)

    # 标准化
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)

    # 定义模型
    models = {
        'ANN': MLPClassifier(hidden_layer_sizes=(100, 50), max_iter=1000, random_state=42),
        'Naive_Bayes': GaussianNB(),
        'Decision_Tree': DecisionTreeClassifier(random_state=42),
        'Random_Forest': RandomForestClassifier(n_estimators=100, random_state=42)
    }

    # 执行交叉验证
    cv_results = perform_cross_validation(X_train, y_train, models, output_dir)

    # 在测试集上评估
    test_results = {}
    y_pred_proba_dict = {}
    trained_models = {}

    for name, model in models.items():
        print(f"训练 {name}...")

        # 训练模型
        model.fit(X_train_scaled, y_train)
        trained_models[name] = model

        # 预测
        y_pred = model.predict(X_test_scaled)
        y_pred_proba = (model.predict_proba(X_test_scaled)[:, 1]
                       if hasattr(model, 'predict_proba') else y_pred)
        y_pred_proba_dict[name] = y_pred_proba

        # 计算指标
        test_results[name] = {
            'accuracy': accuracy_score(y_test, y_pred),
            'precision': precision_score(y_test, y_pred, average='binary', zero_division=0),
            'recall': recall_score(y_test, y_pred, average='binary', zero_division=0),
            'auc': roc_auc_score(y_test, y_pred_proba)
        }

        print(f"{name} - Accuracy: {test_results[name]['accuracy']:.4f}, "
              f"Precision: {test_results[name]['precision']:.4f}, "
              f"Recall: {test_results[name]['recall']:.4f}, "
              f"AUC: {test_results[name]['auc']:.4f}")

    return trained_models, test_results, y_test, y_pred_proba_dict, scaler, cv_results

def create_visualizations(models, test_results, y_test, y_pred_proba_dict, X_test, feature_columns, output_dir):
    """创建可视化图表"""
    print("生成可视化图表...")

    # 1. 模型性能比较
    plt.figure(figsize=(12, 8))
    metrics = ['accuracy', 'precision', 'recall', 'auc']
    model_names = list(test_results.keys())

    x = np.arange(len(metrics))
    width = 0.2

    for i, model_name in enumerate(model_names):
        values = [test_results[model_name][metric] for metric in metrics]
        plt.bar(x + i * width, values, width, label=model_name)

    plt.xlabel('评估指标')
    plt.ylabel('分数')
    plt.title('模型性能比较')
    plt.xticks(x + width * 1.5, ['准确率', '精确率', '召回率', 'AUC'])
    plt.legend()
    plt.tight_layout()
    plt.savefig(f"{output_dir}/model_performance_comparison.png", dpi=300, bbox_inches='tight')
    plt.close()

    # 2. ROC曲线
    plt.figure(figsize=(10, 8))
    for name, y_pred_proba in y_pred_proba_dict.items():
        fpr, tpr, _ = roc_curve(y_test, y_pred_proba)
        auc_score = test_results[name]['auc']
        plt.plot(fpr, tpr, label=f'{name} (AUC = {auc_score:.3f})')

    plt.plot([0, 1], [0, 1], 'k--', label='随机分类器')
    plt.xlabel('假正率 (FPR)')
    plt.ylabel('真正率 (TPR)')
    plt.title('ROC曲线比较')
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plt.savefig(f"{output_dir}/roc_curves.png", dpi=300, bbox_inches='tight')
    plt.close()

    # 3. 混淆矩阵 (以随机森林为例)
    rf_model = models['Random_Forest']
    scaler = StandardScaler()
    X_test_scaled = scaler.fit_transform(X_test)
    y_pred_rf = rf_model.predict(X_test_scaled)

    cm = confusion_matrix(y_test, y_pred_rf)
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=['正常', '异常'], yticklabels=['正常', '异常'])
    plt.title('随机森林混淆矩阵')
    plt.ylabel('真实标签')
    plt.xlabel('预测标签')
    plt.tight_layout()
    plt.savefig(f"{output_dir}/confusion_matrix.png", dpi=300, bbox_inches='tight')
    plt.close()

    # 4. 特征重要性 (随机森林)
    if hasattr(rf_model, 'feature_importances_'):
        feature_importance = rf_model.feature_importances_

        # 选择前10个最重要的特征
        top_indices = np.argsort(feature_importance)[-10:]

        plt.figure(figsize=(10, 6))
        plt.barh(range(len(top_indices)), feature_importance[top_indices])
        plt.yticks(range(len(top_indices)), [feature_columns[i] for i in top_indices])
        plt.xlabel('特征重要性')
        plt.title('随机森林特征重要性 (Top 10)')
        plt.tight_layout()
        plt.savefig(f"{output_dir}/feature_importance.png", dpi=300, bbox_inches='tight')
        plt.close()

    print("可视化图表生成完成！")

def save_results(test_results, cv_results, models, scaler, output_dir):
    """保存结果到文件"""
    print("保存结果...")

    # 保存测试集结果
    test_df = pd.DataFrame(test_results).T
    test_df.to_csv(f"{output_dir}/test_results.csv")

    # 保存详细的交叉验证结果
    detailed_cv = {}
    for model_name in cv_results:
        for metric in cv_results[model_name]:
            detailed_cv[f"{model_name}_{metric}_mean"] = cv_results[model_name][metric]['mean']
            detailed_cv[f"{model_name}_{metric}_std"] = cv_results[model_name][metric]['std']

    pd.DataFrame([detailed_cv]).to_csv(f"{output_dir}/detailed_cv_results.csv", index=False)

    # 保存模型和标准化器
    with open(f"{output_dir}/models_and_scaler.pkl", 'wb') as f:
        pickle.dump({'models': models, 'scaler': scaler}, f)

    # 生成分类报告
    print("生成分类报告...")

    print(f"所有结果已保存到: {output_dir}")

def main():
    """主函数"""
    print("=== 网络异常检测实验 ===")

    # 创建输出目录
    output_dir = create_output_dir()
    print(f"输出目录: {output_dir}")

    # 加载和预处理数据
    X, y, df, feature_columns, le_dict = load_and_preprocess_data()

    # 训练和评估模型
    models, test_results, y_test, y_pred_proba_dict, scaler, cv_results = train_and_evaluate_models(X, y, output_dir)

    # 创建可视化
    X_test = X.iloc[int(len(X) * 2/3):]  # 获取测试集特征用于可视化
    create_visualizations(models, test_results, y_test, y_pred_proba_dict, X_test, feature_columns, output_dir)

    # 保存结果
    save_results(test_results, cv_results, models, scaler, output_dir)

    print("实验完成！")
    print(f"结果保存在: {output_dir}")

    # 打印最终结果摘要
    print("\n=== 实验结果摘要 ===")
    for model_name, metrics in test_results.items():
        print(f"{model_name}:")
        for metric, value in metrics.items():
            print(f"  {metric}: {value:.4f}")
        print()

if __name__ == "__main__":
    main()