#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试TensorFlow和GPU是否正确安装和配置
"""

try:
    import tensorflow as tf
    print("✅ TensorFlow 导入成功")
    print(f"TensorFlow 版本: {tf.__version__}")
    
    # 检查GPU
    gpus = tf.config.experimental.list_physical_devices('GPU')
    if gpus:
        print(f"✅ 发现 {len(gpus)} 个GPU设备:")
        for i, gpu in enumerate(gpus):
            print(f"  GPU {i}: {gpu}")
        
        # 测试GPU内存配置
        try:
            for gpu in gpus:
                tf.config.experimental.set_memory_growth(gpu, True)
            print("✅ GPU内存增长配置成功")
        except RuntimeError as e:
            print(f"⚠️ GPU配置警告: {e}")
    else:
        print("⚠️ 未发现GPU设备，将使用CPU")
    
    # 测试基本操作
    print("\n测试基本TensorFlow操作:")
    with tf.device('/GPU:0' if gpus else '/CPU:0'):
        a = tf.constant([[1.0, 2.0], [3.0, 4.0]])
        b = tf.constant([[1.0, 1.0], [0.0, 1.0]])
        c = tf.matmul(a, b)
        print(f"矩阵乘法结果:\n{c}")
    
    print("✅ TensorFlow测试完成")
    
except ImportError as e:
    print(f"❌ TensorFlow导入失败: {e}")
    print("请安装TensorFlow: pip install tensorflow")
except Exception as e:
    print(f"❌ TensorFlow测试出错: {e}")
