import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split, cross_val_score, RepeatedStratifiedKFold
from sklearn.naive_bayes import GaussianNB
from sklearn.tree import DecisionTreeClassifier
from sklearn.metrics import classification_report, confusion_matrix, roc_curve, auc, accuracy_score, precision_score, recall_score, f1_score
import matplotlib.pyplot as plt
import seaborn as sns
import joblib
import os
import matplotlib
import warnings
import time

warnings.filterwarnings('ignore')
matplotlib.rcParams['font.family'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

# 步骤 1：读取数据
print("\n>>> 步骤 1：读取数据")
df = pd.read_csv('event.csv', encoding='GB18030')
print("数据读取完成，数据形状：", df.shape)

# 步骤 2：清洗数据
print("\n>>> 步骤 2：清洗数据")
df = df.loc[:, ~df.columns.str.contains('IP')]
df = df.loc[:, ~df.columns.str.contains('时间')]
print("数据形状:", df.shape)

# 步骤 3：添加标签列
print("\n>>> 步骤 3：添加标签列")
def multi_class_label(row):
    if isinstance(row['事件类型'], str) and ('攻击' in row['事件类型'] or '刺探' in row['事件类型'] or '漏洞' in row['事件类型']):
        return 1
    if row['事件等级'] in ['严重', '重要']:
        return 1
    if row['结果'] in ['成功', '失败']:
        return 1
    return 0

df['is_anomaly'] = df.apply(multi_class_label, axis=1)

# 步骤 4：划分特征与标签
print("\n>>> 步骤 4：划分特征与标签")
drop_columns = ['is_anomaly', '事件类型', '事件等级', '结果']
X = df.drop(columns=drop_columns)
y = df['is_anomaly']
original_columns = X.columns
print("特征数据形状：", X.shape)
print("标签数据形状：", y.shape)

# 步骤 5：划分训练测试集
print("\n>>> 步骤 5：划分训练测试集")
X_train_raw, X_test_raw, y_train, y_test = train_test_split(X, y, test_size=1/3, stratify=y, random_state=0)

# 步骤 6：处理缺失值
print("\n>>> 步骤 6：处理缺失值")
missing_persentage = X_train_raw.isnull().mean()
columns_to_drop = missing_persentage[missing_persentage > 0.3].index
X_train_raw = X_train_raw.drop(columns=columns_to_drop)
X_test_raw = X_test_raw.drop(columns=columns_to_drop)

numeric_columns = X_train_raw.select_dtypes(include=['number']).columns
for col in numeric_columns:
    train_mean = X_train_raw[col].mean()
    X_train_raw[col].fillna(train_mean, inplace=True)
    X_test_raw[col].fillna(train_mean, inplace=True)

non_numeric_columns = X_train_raw.select_dtypes(exclude=['number']).columns
for col in non_numeric_columns:
    X_train_raw[col].fillna('UNKNOWN', inplace=True)
    X_test_raw[col].fillna('UNKNOWN', inplace=True)

print("处理缺失值完成，训练集形状:", X_train_raw.shape, "测试集形状:", X_test_raw.shape)

# 步骤 7：特征编码（使用 get_dummies）
print("\n>>> 步骤 7：特征编码")
categorical_cols = X_train_raw.select_dtypes(include='object').columns.tolist()
numeric_cols = X_train_raw.select_dtypes(exclude='object').columns.tolist()

X_train_cat = pd.get_dummies(X_train_raw[categorical_cols], drop_first=True)
X_test_cat = pd.get_dummies(X_test_raw[categorical_cols], drop_first=True)
X_train_cat, X_test_cat = X_train_cat.align(X_train_cat, join='outer', axis=1, fill_value=0)

scaler = StandardScaler()
X_train_num = pd.DataFrame(scaler.fit_transform(X_train_raw[numeric_cols]), columns=numeric_cols, index=X_train_raw.index)
X_test_num = pd.DataFrame(scaler.transform(X_test_raw[numeric_cols]), columns=numeric_cols, index=X_test_raw.index)

X_train = pd.concat([X_train_num, X_train_cat], axis=1)
X_test = pd.concat([X_test_num, X_test_cat], axis=1)

print("特征编码完成，训练集形状：", X_train.shape, "测试集形状：", X_test.shape)

# 步骤 8：模型训练与评估
print("\n>>> 步骤 8：模型训练与评估")
models = {
    'Naive Bayes': GaussianNB(),
    'Decision Tree': DecisionTreeClassifier(max_depth=3, min_samples_leaf=5, random_state=42)
}

results = {}
cv = RepeatedStratifiedKFold(n_splits=2, n_repeats=5, random_state=42)

for name, model in models.items():
    print(f"\n>>> 训练模型：{name}")
    train_start_time = time.time()
    model.fit(X_train, y_train)
    train_end_time = time.time()

    y_proba = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else None
    scores = cross_val_score(model, X_train, y_train, cv=cv, scoring='f1')

    test_start_time = time.time()
    y_pred = model.predict(X_test)
    test_end_time = time.time()

    results[name] = {
        'model': model,
        'f1_cv': np.mean(scores),
        'accuracy': accuracy_score(y_test, y_pred),
        'precision': precision_score(y_test, y_pred),
        'recall': recall_score(y_test, y_pred),
        'f1': f1_score(y_test, y_pred),
        'auc': auc(*roc_curve(y_test, y_proba)[:2]) if y_proba is not None else None,
        'y_pred': y_pred,
        'y_proba': y_proba,
        'train_time': train_end_time-train_start_time,
        'test_time': test_end_time-test_start_time
    }

    print(f"{name} 模型评估指标：")
    print(f"F1 (CV): {results[name]['f1_cv']:.4f}")
    print(f"Accuracy: {results[name]['accuracy']:.4f}")
    print(f"Precision: {results[name]['precision']:.4f}")
    print(f"Recall: {results[name]['recall']:.4f}")
    print(f"F1 (Test): {results[name]['f1']:.4f}")
    print(f"train_time: {results[name]['train_time']:.4f} s")
    print(f"test_time: {results[name]['test_time']:.4f} s")
    if y_proba is not None:
        print(f"AUC: {results[name]['auc']:.4f}")

# 步骤 9：可视化 ROC
print("\n>>> 步骤 9：可视化 ROC")
os.makedirs('output', exist_ok=True)
plt.figure(figsize=(10, 6))
for name, res in results.items():
    if res['y_proba'] is not None:
        fpr, tpr, _ = roc_curve(y_test, res['y_proba'])
        plt.plot(fpr, tpr, label=f"{name} (AUC={res['auc']:.2f})")
plt.plot([0, 1], [0, 1], 'k--')
plt.xlabel('假阳性率')
plt.ylabel('真正率')
plt.title('模型对比：ROC 曲线')
plt.legend()
plt.grid()
plt.tight_layout()
plt.savefig('output/roc_comparison.png')
print("ROC 曲线图已保存到 output/roc_comparison.png")

# 步骤 10：输出混淆矩阵
print("\n>>> 步骤 10：输出混淆矩阵")
best_model_name = max(results, key=lambda x: results[x]['f1'])
best_result = results[best_model_name]
best_model = best_result['model']

cm = confusion_matrix(y_test, best_result['y_pred'])
plt.figure(figsize=(6, 4))
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
plt.title(f'混淆矩阵 - 最佳模型：{best_model_name}')
plt.xlabel('预测值')
plt.ylabel('真实值')
plt.tight_layout()
plt.savefig('output/confusion_matrix.png')
print(f"混淆矩阵已保存到 output/confusion_matrix.png")

# 分类报告
print(f"\n>>> 最佳模型: {best_model_name}")
print(classification_report(y_test, best_result['y_pred']))

# 步骤 11：特征重要性
if hasattr(best_model, "feature_importances_"):
    importances = best_model.feature_importances_
    indices = np.argsort(importances)[::-1]
    plt.figure(figsize=(12, 6))
    sns.barplot(x=importances[indices], y=np.array(X_train.columns)[indices])
    plt.title(f'特征重要性 - {best_model_name}')
    plt.tight_layout()
    plt.savefig("output/feature_importance.png")

# 步骤 12：保存结果
df.to_csv('output/cleaned_event.csv', index=False, encoding='GB18030')

metrics_df = pd.DataFrame([
    {
        '模型': name,
        'F1（交叉验证）': res['f1_cv'],
        '准确率': res['accuracy'],
        '精确率': res['precision'],
        '召回率': res['recall'],
        'F1（测试集）': res['f1'],
        'AUC': res['auc'],
        '训练时间': res['train_time'],
        '测试时间': res['test_time']
    }
    for name, res in results.items()
])
metrics_df.to_csv('output/model_metrics.csv', index=False, encoding='GB18030')

print("\n✅ 实验完成，所有结果已保存到 output 文件夹。")