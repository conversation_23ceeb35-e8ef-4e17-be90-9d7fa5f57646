import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score, GridSearchCV
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, roc_curve, f1_score

from sklearn.naive_bayes import GaussianNB
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.neural_network import MLPClassifier

from sklearn.feature_selection import mutual_info_classif
from imblearn.over_sampling import SMOTE  # 用于处理样本不平衡

# 1. 加载数据
df = pd.read_csv('event.csv', encoding="GB18030")
print("数据基本信息：")
print(df.info())
print(df.head())

# 2. 缺失值和重复值处理
df.drop_duplicates(inplace=True)
missing_ratio = df.isnull().mean()
df = df.drop(columns=missing_ratio[missing_ratio > 0.3].index)  # 删除缺失过多的列
df.fillna(df.median(numeric_only=True), inplace=True)

# 3. 编码分类变量
for col in df.select_dtypes(include='object').columns:
    df[col] = df[col].fillna(df[col].mode()[0])
    le = LabelEncoder()
    df[col] = le.fit_transform(df[col])

# 4. 分离特征和标签
X = df.drop('label', axis=1)
y = df['label']

# 5. 标准化数值特征
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)

# 6. 特征选择（互信息）
mi = mutual_info_classif(X_scaled, y)
mi_scores = pd.Series(mi, index=X.columns).sort_values(ascending=False)
selected_features = mi_scores.head(20).index.tolist()  # 选择互信息值Top20的特征
X_selected = X[selected_features]

# 7. 时间顺序划分数据（避免数据泄露）
# 假设数据按时间排列，如没有时间列，使用shuffle=False随机划分
train_size = int(0.67 * len(X_selected))
X_train, X_test = X_selected[:train_size], X_selected[train_size:]
y_train, y_test = y[:train_size], y[train_size:]

# 8. 处理训练集不平衡问题
smote = SMOTE(random_state=42)
X_train_bal, y_train_bal = smote.fit_resample(X_train, y_train)

# 9. 模型与超参数
models = {
    'NaiveBayes': GaussianNB(),
    'DecisionTree': DecisionTreeClassifier(max_depth=10, random_state=42),
    'RandomForest': RandomForestClassifier(n_estimators=100, random_state=42),
    'SVM': SVC(kernel='rbf', probability=True, C=1.0),
    'MLP': MLPClassifier(hidden_layer_sizes=(64, 32), max_iter=300, random_state=42)
}

# 10. 交叉验证和评估
skf = StratifiedKFold(n_splits=10, shuffle=True, random_state=42)

for name, model in models.items():
    scores = cross_val_score(model, X_train_bal, y_train_bal, cv=skf, scoring='f1')
    print(f'{name} 交叉验证 F1 分数：{scores.mean():.4f} ± {scores.std():.4f}')

# 11. 在测试集上验证最佳模型（例如 RandomForest）
best_model = RandomForestClassifier(n_estimators=100, random_state=42)
best_model.fit(X_train_bal, y_train_bal)
y_pred = best_model.predict(X_test)
y_prob = best_model.predict_proba(X_test)[:, 1]

print("\n测试集性能评估：")
print(classification_report(y_test, y_pred))
print("AUC:", roc_auc_score(y_test, y_prob))
print("F1 Score:", f1_score(y_test, y_pred))

# 可视化 ROC 曲线
fpr, tpr, _ = roc_curve(y_test, y_prob)
plt.figure()
plt.plot(fpr, tpr, label='ROC Curve (AUC = {:.4f})'.format(roc_auc_score(y_test, y_prob)))
plt.plot([0, 1], [0, 1], linestyle='--')
plt.xlabel("False Positive Rate")
plt.ylabel("True Positive Rate")
plt.title("ROC Curve - Random Forest")
plt.legend()
plt.show()
