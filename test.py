import pandas as pd
import matplotlib.pyplot as plt
import matplotlib

# 读取CSV文件
def read_csv(file_path):
    data = pd.read_csv(file_path, encoding='GB18030')
    return data

# 统计端口分布
def analyze_port_distribution(data, column_name):
    if column_name not in data.columns:
        print(f"列 {column_name} 不存在于数据中。")
        return None
    # 清理数据并转换为数值类型
    data[column_name] = pd.to_numeric(data[column_name], errors='coerce')
    # 过滤掉负数
    data[column_name] = data[column_name][data[column_name] >= 0]
    port_distribution = data[column_name].value_counts().sort_index()
    return port_distribution

# 统计事件类型分布
def analyze_event_type_distribution(data, column_name):
    if column_name not in data.columns:
        print(f"列 {column_name} 不存在于数据中。")
        return None
    event_type_distribution = data[column_name].value_counts().sort_index()
    return event_type_distribution

# 绘制柱状图并保存
def plot_and_save_distribution(distribution, title, save_path):
    plt.figure(figsize=(12, 8))
    #distribution = distribution.head(20)  # 只取前 20 个
    ax = distribution.plot(kind='bar')
    plt.title(title)
    plt.xlabel("类型")
    plt.ylabel("出现次数")
    plt.xticks(rotation=45)

    # 添加数值标签
    for i, value in enumerate(distribution.values):
        plt.text(i, value, str(value), ha='center', va='bottom', fontsize=10)

    plt.tight_layout()
    plt.savefig(save_path)
    print(f"图表已保存到 {save_path}")
    plt.show()

# 主函数
def main():
    file_path = "event.csv"  # CSV文件路径
    source_port_column = "源端口"  # 源端口列名
    destination_port_column = "目的端口"  # 目的端口列名
    event_type_column = "事件类型"  # 事件类型列名
    event_rank_column = "事件等级"
    beiyong = "备用整形2"

    # 设置中文字体（适配 Windows / Mac / Linux）
    matplotlib.rcParams['font.family'] = ['SimHei']  # SimHei 支持中文
    matplotlib.rcParams['axes.unicode_minus'] = False  # 正确显示负号

    # 读取数据
    data = read_csv(file_path)
    print("\n读取完成，开始进行统计")

    # 分析源端口分布
    '''
    source_port_distribution = analyze_port_distribution(data, source_port_column)
    if source_port_distribution is not None:
        plot_and_save_distribution(
            source_port_distribution,
            "源端口分布",
            "source_port_distribution.png"
        )

    # 分析目的端口分布
    # destination_port_distribution = analyze_port_distribution(data, destination_port_column)
    # if destination_port_distribution is not None:
        plot_and_save_distribution(
            destination_port_distribution,
            "目的端口分布",
            "destination_port_distribution.png"
        )
    
    # 分析事件等级分布
    event_rank_distribution = analyze_event_type_distribution(data, event_rank_column)
    if event_rank_distribution is not None:
        plot_and_save_distribution(
            event_rank_distribution,
            "事件等级分布",
            "event_rank_distribution.png"
        )
    # 分析事件类型分布
    
    event_type_distribution = analyze_event_type_distribution(data, event_type_column)
    if event_type_distribution is not None:
        plot_and_save_distribution(
            event_type_distribution,
            "事件类型分布",
            "event_type_distribution.png"
        )
    '''
    # 分析备用字符串分布
    
    b = analyze_event_type_distribution(data, beiyong)
    if b is not None :
        plot_and_save_distribution (
            b,
            "备用字符串1分布", 
            "b.png"
        )


if __name__ == "__main__":
    main()