import pandas as pd
import numpy as np
from sklearn.preprocessing import OrdinalEncoder, StandardScaler, LabelEncoder, OneHotEncoder
from sklearn.model_selection import train_test_split, cross_val_score, RepeatedStratifiedKFold
from sklearn.naive_bayes import GaussianNB
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import RandomForestClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.metrics import classification_report, confusion_matrix, roc_curve, auc, accuracy_score, precision_score, recall_score, f1_score
import matplotlib.pyplot as plt
import seaborn as sns
import joblib
import os
import matplotlib
import warnings
import time

warnings.filterwarnings('ignore')

# 设置中文字体（适配 Windows / Mac / Linux）
matplotlib.rcParams['font.family'] = ['SimHei']  # SimHei 支持中文
matplotlib.rcParams['axes.unicode_minus'] = False  # 正确显示负号

# 步骤 1：读取数据
print("\n>>> 步骤 1：读取数据")
df = pd.read_csv('event.csv', encoding='GB18030')
print("数据读取完成，数据形状：", df.shape)

# 步骤 2：清洗数据
print("\n>>> 步骤 2：清洗数据")
# 删除包含 'IP' 文本的列
# df = df.loc[:, ~df.columns.str.contains('端口')]
df = df.loc[:, ~df.columns.str.contains('IP')]
df = df.loc[:, ~df.columns.str.contains('时间')]

missing_persentage = df.isnull().mean()

columns_to_drop = missing_persentage[missing_persentage > 0.3].index
df.drop(columns=columns_to_drop, inplace=True)

print("删除存在缺失值的列后，数据形状：", df.shape)
# 填充剩余列的缺失值，其中：数值类的使用均值填充，非数值类的使用UNKNOW填充
numeric_columns = df.select_dtypes(include=['number']).columns
df[numeric_columns] = df[numeric_columns].fillna(df[numeric_columns].mean())

non_numeric_columns = df.select_dtypes(exclude=['number']).columns
df[non_numeric_columns] = df[non_numeric_columns].fillna('UNKNOWN')
print("数据清洗完成，处理后的数据形状：", df.shape)

#unique_counts = df.nunique()
#single_unique_columns = unique_counts[unique_counts==1].index
#df = df.drop(columns=single_unique_columns)
print("清除唯一值后，数据形状:", df.shape)

# 步骤 2：添加标签列
print("\n>>> 步骤 2：添加标签列")
def multi_class_label(row):
    # 检查“事件类型”是否包含“网络攻击”或“信息刺探”
    if isinstance(row['事件类型'], str) and ('攻击' in row['事件类型'] or '刺探' in row['事件类型'] or '漏洞' in row['事件类型']):
        return 1
    
    
    # 检查“事件等级”是否为“严重”或“重要”
    if row['事件等级'] in ['严重', '重要']:
        return 1
    
    # 检查“结果”是否为“成功”或“失败”
    if row['结果'] in ['成功', '失败']:
        return 1
    
    
    
    # 如果以上条件都不满足，则标记为正常
    return 0

'''
# 确保“事件类型”、“事件等级”和“操作”列存在
if all(col in df.columns for col in ['事件类型', '事件等级', '结果']):
    df['is_anomaly'] = df.apply(multi_class_label, axis=1)
    print("标签列添加完成，数据形状：", df.shape)
else:
    raise ValueError("缺少必要的列：'事件类型', '事件等级', 或 '结果'")
'''

df['is_anomaly'] = df.apply(multi_class_label, axis=1)
# 步骤 3：划分特征与标签
print("\n>>> 步骤 3：划分特征与标签")

# 删除特征列

# 删除高度相关的特征列
drop_columns = ['is_anomaly', '事件类型', '事件等级', '结果'] 
X = df.drop(columns=drop_columns)
y = df['is_anomaly']

print("特征数据形状：", X.shape)
print("标签数据形状：", y.shape)

# 保留原始列名用于输出
original_columns = X.columns

# 步骤 6：划分训练测试集
print("\n>>> 步骤 6：划分训练测试集")
X_train_raw, X_test_raw, y_train, y_test = train_test_split(X, y, test_size=1/3, stratify=y, random_state=0)
print("训练集特征形状：", X_train_raw.shape)
print("测试集特征形状：", X_test_raw.shape)
print("训练集标签形状：", y_train.shape)
print("测试集标签形状：", y_test.shape)


# 步骤 7：特征编码
print("\n>>> 步骤 7：特征编码")

X_train = X_train_raw.copy()
X_test = X_test_raw.copy()
categorical_cols = [col for col in X.columns if X[col].dtype == 'object']
numeric_cols = [col for col in X.columns if X[col].dtype != 'object']

print("分类特征列：", categorical_cols)


# 为每一列建立一个 LabelEncoder 实例（方便还原或保存）
label_encoders = {}

for col in categorical_cols:
    le = LabelEncoder()
    
    # 合并训练测试避免 unseen label（也可以在测试集中设置为 -1）
    combined = pd.concat([X_train[col], X_test[col]], axis=0).astype(str)
    le.fit(combined)

    # 分别转换训练集和测试集
    X_train[col] = le.transform(X_train[col].astype(str))
    X_test[col] = le.transform(X_test[col].astype(str))
    
    label_encoders[col] = le

print("使用 LabelEncoder 完成分类特征编码")


# 步骤 9：模型训练与评估
print("\n>>> 步骤 9：模型训练与评估")
models = {
    'Naive Bayes': GaussianNB(),
    'Decision Tress': DecisionTreeClassifier(max_depth=3, min_samples_leaf=5, random_state=42),
    #'Random Forest': RandomForestClassifier(max_depth=10,min_samples_split=10,random_state=42),
    # 'ANN': MLPClassifier(hidden_layer_sizes=(100,), max_iter=300, random_state=42, early_stopping=True)
}

results = {}
cv = RepeatedStratifiedKFold(n_splits=2, n_repeats=5, random_state=42)

for name, model in models.items():        
    print(f"\n>>> 训练模型：{name}")

    train_start_time = time.time()
    model.fit(X_train, y_train)
    train_end_time = time.time()

    
    y_proba = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else None
    scores = cross_val_score(model, X_train, y_train, cv=cv, scoring='f1')

    test_start_time = time.time()
    y_pred = model.predict(X_test)
    test_end_time = time.time()

    results[name] = {
        'model': model,
        'f1_cv': np.mean(scores),
        'accuracy': accuracy_score(y_test, y_pred),
        'precision': precision_score(y_test, y_pred),
        'recall': recall_score(y_test, y_pred),
        'f1': f1_score(y_test, y_pred),
        'auc': auc(*roc_curve(y_test, y_proba)[:2]) if y_proba is not None else None,
        'y_pred': y_pred,
        'y_proba': y_proba,
        'train_time': train_end_time-train_start_time,
        'test_time': test_end_time-test_start_time
    }

    print(f"{name} 模型评估指标：")
    print(f"F1 (CV): {results[name]['f1_cv']:.4f}")
    print(f"Accuracy: {results[name]['accuracy']:.4f}")
    print(f"Precision: {results[name]['precision']:.4f}")
    print(f"Recall: {results[name]['recall']:.4f}")
    print(f"F1 (Test): {results[name]['f1']:.4f}")
    print(f"train_time: {results[name]['train_time']:.4f} s")
    print(f"test_time: {results[name]['test_time']:.4f} s")
    if y_proba is not None:
        print(f"AUC: {results[name]['auc']:.4f}")

# 步骤 10：可视化 ROC
print("\n>>> 步骤 10：可视化 ROC")
os.makedirs('output', exist_ok=True)
plt.figure(figsize=(10, 6))
for name, res in results.items():
    if res['y_proba'] is not None:
        fpr, tpr, _ = roc_curve(y_test, res['y_proba'])
        plt.plot(fpr, tpr, label=f"{name} (AUC={res['auc']:.2f})")
plt.plot([0, 1], [0, 1], 'k--')
plt.xlabel('假阳性率')
plt.ylabel('真正率')
plt.title('模型对比：ROC 曲线')
plt.legend()
plt.grid()
plt.tight_layout()
plt.savefig('output/roc_comparison.png')
print("ROC 曲线图已保存到 output/roc_comparison.png")

# 步骤 11：输出混淆矩阵
print("\n>>> 步骤 11：输出混淆矩阵")
best_model_name = max(results, key=lambda x: results[x]['f1'])
best_result = results[best_model_name]
best_model = best_result['model']

cm = confusion_matrix(y_test, best_result['y_pred'])
plt.figure(figsize=(6, 4))
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
plt.title(f'混淆矩阵 - 最佳模型：{best_model_name}')
plt.xlabel('预测值')
plt.ylabel('真实值')
plt.tight_layout()
plt.savefig('output/confusion_matrix.png')
print(f"混淆矩阵已保存到 output/confusion_matrix.png")

# 分类报告
print(f"\n>>> 最佳模型: {best_model_name}")
print(classification_report(y_test, best_result['y_pred']))

# 步骤 12：特征重要性（如果适用）
if hasattr(best_model, "feature_importances_"):
    importances = best_model.feature_importances_
    indices = np.argsort(importances)[::-1]
    plt.figure(figsize=(12, 6))
    sns.barplot(x=importances[indices], y=np.array(original_columns)[indices])
    plt.title(f'特征重要性 - {best_model_name}')
    plt.tight_layout()
    plt.savefig("output/feature_importance.png")

# 步骤 13：保存结果
df.to_csv('output/cleaned_event.csv', index=False, encoding='GB18030')  # 保存中文字段

metrics_df = pd.DataFrame([
    {
        '模型': name,
        'F1（交叉验证）': res['f1_cv'],
        '准确率': res['accuracy'],
        '精确率': res['precision'],
        '召回率': res['recall'],
        'F1（测试集）': res['f1'],
        'AUC': res['auc'],
        '训练时间': res['train_time'],
        '测试时间': res['test_time']
    }
    for name, res in results.items()
])
metrics_df.to_csv('output/model_metrics.csv', index=False, encoding='GB18030')

print("\n✅ 实验完成，所有结果已保存到 output 文件夹。")
