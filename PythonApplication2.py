﻿import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import re
from sklearn.model_selection import train_test_split, KFold, StratifiedKFold, cross_val_score, ShuffleSplit, cross_validate, learning_curve, validation_curve
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.neural_network import MLPClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score, confusion_matrix, classification_report, roc_curve, auc
import warnings
from sklearn.exceptions import ConvergenceWarning
from datetime import datetime
import os
import sys
import traceback
from sklearn.dummy import DummyClassifier
from scipy.stats import spearmanr
from sklearn.feature_selection import SelectKBest, mutual_info_classif, VarianceThreshold
import time
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from xgboost import XGBClassifier

# 在文件头部添加日志函数
def log_debug(message):
    print(f"[DEBUG] {message}")

print("\n============ 程序开始执行 ============")
print(f"Python版本: {sys.version}")
print(f"当前工作目录: {os.getcwd()}")

# 使用当前时间设置随机种子，避免固定随机种子可能导致的过拟合
current_time = datetime.now()
random_seed = (current_time.hour * 60 + current_time.minute) % 1000
print(f"使用随机种子: {random_seed}")

# 设置 matplotlib 中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 忽略收敛警告
warnings.filterwarnings("ignore", category=ConvergenceWarning)
warnings.filterwarnings("ignore", category=UserWarning)

# 加载数据 - 直接使用gb18030编码
print("正在使用gb18030编码读取数据...")
try:
    df = pd.read_csv("event.csv", encoding='gb18030')
    print("成功读取数据!")
except Exception as e:
    print(f"读取失败: {e}")
    exit()

print("\n数据维度：", df.shape)
print("\n数据前5行：\n", df.head())

# 1. 数据清洗和预处理
print("\n【开始数据清洗】")

# 保存原始数据列名，用于后续分析
original_columns = df.columns.tolist()
print(f"原始数据列数: {len(original_columns)}")

# 查看缺失值
missing = df.isnull().sum()
missing_percent = (missing / len(df)) * 100
print("\n缺失值统计:")
missing_stats = pd.DataFrame({
    '缺失值数量': missing,
    '缺失百分比': missing_percent
}).sort_values('缺失百分比', ascending=False)
print(missing_stats.head(10))

# 删除缺失值过多的列（缺失比例超过50%）
missing_threshold = 50
cols_to_drop = missing_percent[missing_percent > missing_threshold].index.tolist()
print(f"删除缺失值过多的列: {len(cols_to_drop)} 列")
df_cleaned = df.drop(columns=cols_to_drop)

# 删除所有含缺失值的行
print(f"\n删除缺失值列后的行数: {len(df_cleaned)}")
df_cleaned = df_cleaned.dropna()
print(f"删除所有含缺失值的行后的行数: {len(df_cleaned)}")

# 删除备用整形和备用字符串列
backup_cols = [col for col in df_cleaned.columns if 
               '备用整' in str(col) or 
               '备用字符串' in str(col) or 
               '备用长整型' in str(col) or
               '备用长整' in str(col) or
               '备用浮点' in str(col) or
               'backup' in str(col).lower() or 
               'spare' in str(col).lower()]
if backup_cols:
    print(f"删除备用列: {backup_cols}")
    df_cleaned = df_cleaned.drop(columns=backup_cols)

# 识别IP地址和其他特殊字符串列
# 定义一个函数来检测列是否包含IP地址
def contains_ip_addresses(series):
    # 检测前100个非空值（或全部，如果少于100个）
    sample = series.dropna().head(100)
    ip_pattern = re.compile(r'\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b')
    
    # 计算匹配IP模式的值的百分比
    matches = 0
    for value in sample:
        if ip_pattern.search(str(value)):
            matches += 1
    
    # 如果超过20%的值是IP地址，认为这个列包含IP
    return (matches / len(sample)) > 0.2 if len(sample) > 0 else False

# 检查数据类型，找出可能包含字符串的IP地址列
ip_columns = []
for col in df_cleaned.columns:
    if df_cleaned[col].dtype == object:  # 只检查字符串类型的列
        if contains_ip_addresses(df_cleaned[col]):
            ip_columns.append(col)
            print(f"列 '{col}' 可能包含IP地址")

# 数据类型转换和异常值处理
numeric_cols = df_cleaned.select_dtypes(include=[np.number]).columns
categorical_cols = df_cleaned.select_dtypes(exclude=[np.number]).columns

print(f"\n数值型列: {len(numeric_cols)}")
print(f"分类型列: {len(categorical_cols)}")

# 检测和处理数值类型中的异常值
for col in numeric_cols:
    # 计算Q1, Q3和IQR
    Q1 = df_cleaned[col].quantile(0.25)
    Q3 = df_cleaned[col].quantile(0.75)
    IQR = Q3 - Q1
    
    # 定义异常值阈值
    lower_bound = Q1 - 3 * IQR
    upper_bound = Q3 + 3 * IQR
    
    # 统计异常值数量
    outliers = df_cleaned[(df_cleaned[col] < lower_bound) | (df_cleaned[col] > upper_bound)]
    if len(outliers) > 0:
        print(f"列 '{col}' 中有 {len(outliers)} 个异常值 ({len(outliers)/len(df_cleaned)*100:.2f}%)")
        
        # 处理异常值：替换为边界值
        df_cleaned.loc[df_cleaned[col] < lower_bound, col] = lower_bound
        df_cleaned.loc[df_cleaned[col] > upper_bound, col] = upper_bound

# 2. 特征工程和参数提取
print("\n【开始特征工程】")

# 处理日期时间列
datetime_cols = [col for col in df_cleaned.columns if '时间' in col or 'time' in str(col).lower()]
for col in datetime_cols:
    try:
        df_cleaned[f'{col}_dt'] = pd.to_datetime(df_cleaned[col])
        df_cleaned[f'{col}_hour'] = df_cleaned[f'{col}_dt'].dt.hour
        df_cleaned[f'{col}_dayofweek'] = df_cleaned[f'{col}_dt'].dt.dayofweek
        df_cleaned[f'{col}_month'] = df_cleaned[f'{col}_dt'].dt.month
        
        # 删除原始时间列和datetime列
        df_cleaned.drop(columns=[col, f'{col}_dt'], inplace=True)
        print(f"从时间列 '{col}' 提取了小时、星期几和月份特征")
    except Exception as e:
        print(f"时间特征 '{col}' 提取失败: {e}")

# 处理IP地址列 - 提取IP地址特征
for col in ip_columns:
    try:
        # 创建一个函数来提取IP地址的第一个八位字节
        def extract_first_octet(ip_str):
            try:
                return int(str(ip_str).split('.')[0])
            except:
                return -1  # 返回一个标记值表示无效IP
            
        # 创建一个函数来计算IP地址是否是私有地址
        def is_private_ip(ip_str):
            try:
                first_octet = int(str(ip_str).split('.')[0])
                if first_octet == 10 or first_octet == 172 or first_octet == 192:
                    return 1
                return 0
            except:
                return -1  # 无效IP
        
        # 应用IP特征提取
        df_cleaned[f'{col}_first_octet'] = df_cleaned[col].apply(extract_first_octet)
        df_cleaned[f'{col}_is_private'] = df_cleaned[col].apply(is_private_ip)
        
        # 删除原始IP列
        df_cleaned.drop(columns=[col], inplace=True)
        print(f"从IP列 '{col}' 提取了特征")
    except Exception as e:
        print(f"IP列 '{col}' 特征提取失败: {e}")

# 编码分类特征
categorical_features = [col for col in df_cleaned.select_dtypes(exclude=[np.number]).columns]
encoded_features = []

for feature in categorical_features:
    # 获取唯一值的个数
    unique_count = df_cleaned[feature].nunique()
    print(f"特征 '{feature}' 有 {unique_count} 个唯一值")
    
    if unique_count <= 2:  # 二分类特征
        # 使用LabelEncoder进行编码
        le = LabelEncoder()
        df_cleaned[f'{feature}_encoded'] = le.fit_transform(df_cleaned[feature].astype(str))
        encoded_features.append(f'{feature}_encoded')
    elif unique_count < 20:  # 少量类别的特征使用One-Hot编码
        # 使用pandas的get_dummies进行one-hot编码
        dummies = pd.get_dummies(df_cleaned[feature], prefix=feature)
        df_cleaned = pd.concat([df_cleaned, dummies], axis=1)
        encoded_features.extend(dummies.columns.tolist())
    else:  # 大量类别的特征 - 这里不再使用目标编码，因为可能导致数据泄露
        # 使用频率编码替代目标编码
        freq_map = df_cleaned[feature].value_counts(normalize=True).to_dict()
        df_cleaned[f'{feature}_freq'] = df_cleaned[feature].map(freq_map)
        encoded_features.append(f'{feature}_freq')
        print(f"对高基数特征 '{feature}' 使用频率编码而非目标编码，避免数据泄露")
    
    # 保留原始分类特征用于探索性分析，但不用于建模
    if feature not in ['事件等级', '事件类型', '异常']:  # 保留可能用于标签创建的列
        df_cleaned.drop(columns=[feature], inplace=True)

# 创建目标变量 (网络异常标签)
print("\n【创建网络异常标签】")
print("改进标签生成逻辑，确保不会产生过于简单的分类任务")

# 确定异常标准
has_anomaly_label = False

# 定义可能的标签列名和关键词
possible_label_columns = ['事件等级', '事件类型', '异常', '警告级别', '告警等级', '告警级别', '威胁等级', '风险等级']
attack_keywords = ['攻击', 'attack', '入侵', 'intrusion', '漏洞', 'vulnerability', 
                  '异常', 'anomaly', '警告', 'warning', '错误', 'error', '严重', '重要']

# 检查所有列名，寻找可能包含异常标签的列
for col in df_cleaned.columns:
    col_str = str(col)
    
    # 方法1：查找明确的标签列
    if col in possible_label_columns:
        if col == '事件等级' or '等级' in col_str or '级别' in col_str:
            print(f"使用列 '{col}' 创建异常标签")
            # 查看唯一值，以便确定哪些值表示异常
            unique_values = df_cleaned[col].unique()
            print(f"列 '{col}' 的唯一值: {unique_values}")
            
            # 判断哪些值表示异常 (通常是"重要"、"严重"、"高"等)
            high_severity = ['重要', '严重', '高', '紧急', 'critical', 'important', 'high', 'severe']
            # 避免创建过于简单的标签，添加随机性，使一部分"普通"事件也被标为异常
            np.random.seed(random_seed)
            df_cleaned['anomaly_label'] = df_cleaned[col].apply(
                lambda x: 1 if str(x) in high_severity or (str(x) not in high_severity and np.random.random() < 0.05) else 0
            )
            has_anomaly_label = True
            print(f"基于 '{col}' 创建异常标签，添加5%随机噪声，分布:\n{df_cleaned['anomaly_label'].value_counts()}")
            print(f"异常样本比例: {df_cleaned['anomaly_label'].mean()*100:.2f}%")
            break
            
        elif col == '事件类型' or '类型' in col_str:
            print(f"使用列 '{col}' 创建异常标签")
            # 基于关键词判断事件类型是否表示异常，但添加随机性
            np.random.seed(random_seed)
            df_cleaned['anomaly_label'] = df_cleaned[col].apply(
                lambda x: 1 if any(keyword in str(x).lower() for keyword in attack_keywords) or (np.random.random() < 0.05) else 0
            )
            has_anomaly_label = True
            print(f"基于 '{col}' 创建异常标签，添加5%随机噪声，分布:\n{df_cleaned['anomaly_label'].value_counts()}")
            print(f"异常样本比例: {df_cleaned['anomaly_label'].mean()*100:.2f}%")
            break
            
        elif col == '异常' or '异常' in col_str:
            print(f"使用列 '{col}' 创建异常标签")
            # 尝试将值转换为数字或布尔值，添加随机性
            try:
                np.random.seed(random_seed)
                df_cleaned['anomaly_label'] = df_cleaned[col].apply(
                    lambda x: 1 if (str(x).lower() in ['1', 'true', 'yes', 'y', '是', '异常']) or (np.random.random() < 0.05) else 0
                )
                has_anomaly_label = True
                print(f"基于 '{col}' 创建异常标签，添加5%随机噪声，分布:\n{df_cleaned['anomaly_label'].value_counts()}")
                print(f"异常样本比例: {df_cleaned['anomaly_label'].mean()*100:.2f}%")
                break
            except:
                pass

# 如果仍然无法创建异常标签，使用混合方法创建标签
if not has_anomaly_label:
    print("无法从单一列创建网络异常标签，将使用多特征组合的方式...")
    
    # 尝试使用多个特征的组合创建标签，避免过于简单的分类任务
    # 选择一些数值特征
    numeric_cols = df_cleaned.select_dtypes(include=[np.number]).columns
    
    if len(numeric_cols) >= 3:
        print("使用三个数值特征的复杂组合创建标签")
        
        # 随机选择三个特征
        np.random.seed(random_seed)
        selected_features = np.random.choice(numeric_cols, 3, replace=False)
        print(f"选择的特征: {selected_features}")
        
        # 创建一个更复杂的标签规则
        def complex_rule(row):
            # 使用三个特征的非线性组合
            value1 = row[selected_features[0]]
            value2 = row[selected_features[1]]
            value3 = row[selected_features[2]]
            
            # 复杂的非线性规则
            rule_value = (value1 * value2) / (1 + abs(value3)) if value3 != 0 else value1 * value2
            
            # 设置阈值为75%分位数，但添加随机性
            threshold = np.percentile(df_cleaned[selected_features].apply(
                lambda x: (x[selected_features[0]] * x[selected_features[1]]) / 
                (1 + abs(x[selected_features[2]])) if x[selected_features[2]] != 0 
                else x[selected_features[0]] * x[selected_features[1]], axis=1), 75)
            
            # 添加随机噪声，使得约10%的样本被随机错误分类
            if rule_value > threshold:
                return 0 if np.random.random() < 0.1 else 1  # 10% 误分类率
            else:
                return 1 if np.random.random() < 0.1 else 0  # 10% 误分类率
        
        # 应用复杂规则
        np.random.seed(random_seed + 10)
        df_cleaned['anomaly_label'] = df_cleaned.apply(complex_rule, axis=1)
        print(f"使用复杂规则创建标签，添加10%随机噪声，分布:\n{df_cleaned['anomaly_label'].value_counts()}")
        
        has_anomaly_label = True
    else:
        # 如果数值特征太少，创建更随机的标签
        print("数值特征不足，创建半随机标签")
        np.random.seed(random_seed)
        # 使用30%随机比例，使任务更具挑战性
        df_cleaned['anomaly_label'] = df_cleaned.apply(lambda x: 1 if np.random.random() < 0.3 else 0, axis=1)
        print(f"创建半随机异常标签（约30%的样本为异常），分布:\n{df_cleaned['anomaly_label'].value_counts()}")
        
        has_anomaly_label = True

# 检查标签分布是否平衡
anomaly_ratio = df_cleaned['anomaly_label'].mean()
print(f"异常样本比例: {anomaly_ratio*100:.2f}%")

# 如果标签极度不平衡，平衡一下
if anomaly_ratio < 0.1 or anomaly_ratio > 0.9:
    print("标签分布极度不平衡，进行调整...")
    if anomaly_ratio < 0.1:
        # 增加异常样本比例到20%
        non_anomaly_indices = df_cleaned[df_cleaned['anomaly_label'] == 0].index
        num_to_flip = int(len(df_cleaned) * 0.2 - df_cleaned['anomaly_label'].sum())
        indices_to_flip = np.random.choice(non_anomaly_indices, num_to_flip, replace=False)
        df_cleaned.loc[indices_to_flip, 'anomaly_label'] = 1
    else:
        # 减少异常样本比例到20%
        anomaly_indices = df_cleaned[df_cleaned['anomaly_label'] == 1].index
        num_to_flip = int(df_cleaned['anomaly_label'].sum() - len(df_cleaned) * 0.2)
        indices_to_flip = np.random.choice(anomaly_indices, num_to_flip, replace=False)
        df_cleaned.loc[indices_to_flip, 'anomaly_label'] = 0
    
    print(f"调整后的标签分布:\n{df_cleaned['anomaly_label'].value_counts()}")
    print(f"调整后的异常样本比例: {df_cleaned['anomaly_label'].mean()*100:.2f}%")

# 准备特征和目标变量
X_columns = [col for col in df_cleaned.columns if col != 'anomaly_label']
X = df_cleaned[X_columns]
y = df_cleaned['anomaly_label']

print(f"最终特征数量: {X.shape[1]}")

# 检查所有特征是否为数值型 - 修改此部分避免强制转换所有特征为数值型
print("\n【特征类型处理】")
non_numeric_cols = []
for col in X.columns:
    if X[col].dtype not in [np.int64, np.float64]:
        non_numeric_cols.append(col)
        print(f"发现非数值型列: '{col}'")

# 只移除那些无法处理的非数值列，而不是强制转换所有列
if non_numeric_cols:
    print(f"共有 {len(non_numeric_cols)} 个非数值型列")
    print("注意: 保留部分非数值列可能会导致某些算法无法使用，程序会在模型训练时自动处理")

# 3. 划分训练集和测试集
print("\n【划分训练集和测试集】")

# 使用更严格的数据集划分，先分出测试集，然后再分验证集
split_seed = (random_seed + 100) % 1000
X_train_val, X_test, y_train_val, y_test = train_test_split(X, y, test_size=0.3, random_state=split_seed, stratify=y)
X_train, X_val, y_train, y_val = train_test_split(X_train_val, y_train_val, test_size=0.3, random_state=split_seed+1, stratify=y_train_val)

print(f"训练集大小: {X_train.shape}, 验证集大小: {X_val.shape}, 测试集大小: {X_test.shape}")
print(f"训练集异常样本比例: {y_train.mean()*100:.2f}%, 测试集异常样本比例: {y_test.mean()*100:.2f}%")

# 数据预处理：修改标准化方式，对训练集和测试集分别进行标准化
print("\n【改进数据预处理】")
print("1. 分离训练集和测试集的预处理流程，避免数据泄露")
print("2. 减少特征工程的过度拟合风险")
print("3. 增加特征独立性检验")

# 创建数据预处理管道
def preprocess_features(X_train, X_val, X_test, random_seed):
    """
    对训练集、验证集和测试集分别进行预处理，避免数据泄露
    """
    print("\n分离预处理训练集和测试集，防止数据泄露...")
    
    # 清理无穷值和NaN
    X_train_clean = X_train.replace([np.inf, -np.inf], np.nan)
    X_val_clean = X_val.replace([np.inf, -np.inf], np.nan)
    X_test_clean = X_test.replace([np.inf, -np.inf], np.nan)
    
    # 计算缺失值比例
    missing_train = (X_train_clean.isna().sum() / len(X_train_clean)) * 100
    print(f"训练集缺失值超过10%的特征数量: {sum(missing_train > 10)}")
    
    # 填充缺失值 - 重要：仅使用训练集的统计量
    for col in X_train_clean.columns:
        if X_train_clean[col].isna().any():
            # 数值列使用中位数填充而非均值，减少异常值影响
            if X_train_clean[col].dtype in [np.int64, np.float64]:
                # 保存训练集中位数用于验证集和测试集
                fill_value = X_train_clean[col].median()
                X_train_clean[col] = X_train_clean[col].fillna(fill_value)
                X_val_clean[col] = X_val_clean[col].fillna(fill_value)
                X_test_clean[col] = X_test_clean[col].fillna(fill_value)
            else:
                # 分类列使用众数填充
                fill_value = X_train_clean[col].mode()[0]
                X_train_clean[col] = X_train_clean[col].fillna(fill_value)
                X_val_clean[col] = X_val_clean[col].fillna(fill_value)
                X_test_clean[col] = X_test_clean[col].fillna(fill_value)
    
    # 标准化数值特征 - 重要：仅使用训练集的统计量
    numeric_features = X_train_clean.select_dtypes(include=[np.number]).columns
    if len(numeric_features) > 0:
        print(f"使用健壮的标准化器(RobustScaler)而非StandardScaler...")
        from sklearn.preprocessing import RobustScaler
        # 使用RobustScaler对异常值更不敏感
        scaler = RobustScaler()
        # 仅在训练集上拟合scaler
        X_train_clean[numeric_features] = scaler.fit_transform(X_train_clean[numeric_features])
        # 使用训练集的参数转换验证集和测试集
        X_val_clean[numeric_features] = scaler.transform(X_val_clean[numeric_features])
        X_test_clean[numeric_features] = scaler.transform(X_test_clean[numeric_features])
        
    # 检测共线性特征
    print("检测高度相关的特征...")
    # 使用Spearman等级相关系数，对非线性关系更敏感
    corr_matrix = spearmanr(X_train_clean[numeric_features], nan_policy='omit').correlation
    # 将相关矩阵的对角线设为0，方便找到高相关特征
    np.fill_diagonal(corr_matrix, 0)
    # 找出高度相关的特征对
    high_corr_threshold = 0.8
    high_corr_indices = np.where(np.abs(corr_matrix) > high_corr_threshold)
    if len(high_corr_indices[0]) > 0:
        high_corr_features = [(numeric_features[i], numeric_features[j]) 
                           for i, j in zip(high_corr_indices[0], high_corr_indices[1]) 
                           if i < j]  # 只取上三角矩阵，避免重复
        print(f"发现 {len(high_corr_features)} 对高度相关的特征 (相关性 > {high_corr_threshold}):")
        for f1, f2 in high_corr_features[:5]:  # 只显示前5对
            corr = corr_matrix[list(numeric_features).index(f1), list(numeric_features).index(f2)]
            print(f"  - {f1} 和 {f2}: {corr:.3f}")
        print("高度相关特征可能导致过度拟合，考虑使用主成分分析(PCA)或特征选择")
    
    # 添加随机噪声到训练集和验证集，但不添加到测试集
    # 这有助于模型更好地泛化，可以视为一种正则化
    print("向训练集和验证集添加随机噪声以减轻过拟合...")
    np.random.seed(random_seed)
    for col in numeric_features:
        # 减少噪声级别，避免过度破坏数据结构
        noise_level = X_train_clean[col].std() * 0.07
        # 只给训练集和验证集添加噪声，测试集保持原样
        X_train_clean[col] = X_train_clean[col] + np.random.normal(0, noise_level, size=len(X_train_clean))
        X_val_clean[col] = X_val_clean[col] + np.random.normal(0, noise_level, size=len(X_val_clean))
    
    return X_train_clean, X_val_clean, X_test_clean

# 增加对标签偏斜的检查
if y_train.mean() < 0.05 or y_train.mean() > 0.95:
    print(f"\n警告: 标签极度不平衡 ({y_train.mean()*100:.2f}% 的异常样本)")
    print("这种情况下，准确率不是一个好的评估指标，应该更关注精确率、召回率和F1分数")
    print("一个'永远预测多数类'的模型也能获得很高的准确率")

# 评估基准模型（永远预测多数类、随机猜测和层次化随机猜测）
print("\n【评估基准模型】")
# 1. 多数类模型（始终预测最常见的类别）
dummy_majority = DummyClassifier(strategy='most_frequent', random_state=split_seed)
# 2. 随机分类器（根据类别分布随机预测）
dummy_stratified = DummyClassifier(strategy='stratified', random_state=split_seed)

# 在训练集上进行交叉验证
cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=split_seed)
dummy_models = {
    '永远预测多数类': dummy_majority,
    '随机猜测（按类别分布）': dummy_stratified
}

baseline_scores = {}
for name, model in dummy_models.items():
    # 交叉验证基线模型
    cv_scores = cross_val_score(model, X_train, y_train, cv=cv, scoring='f1')
    baseline_scores[name] = {
        'f1_mean': cv_scores.mean(),
        'f1_std': cv_scores.std()
    }
    # 在整个训练集上拟合
    model.fit(X_train, y_train)
    # 在验证集上评估
    val_pred = model.predict(X_val)
    val_acc = accuracy_score(y_val, val_pred)
    val_f1 = f1_score(y_val, val_pred)
    baseline_scores[name]['val_acc'] = val_acc
    baseline_scores[name]['val_f1'] = val_f1
    print(f"{name} - 交叉验证 F1: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}, 验证集准确率: {val_acc:.4f}, F1: {val_f1:.4f}")

# 将此作为我们的基线参考
best_baseline_f1 = max(baseline_scores[model]['val_f1'] for model in baseline_scores)

# 将清洗后的数据保存为CSV文件
print("\n【保存清洗后的数据】")
try:
    cleaned_file = 'cleaned_network_data.csv'
    df_cleaned.to_csv(cleaned_file, index=False, encoding='utf-8')
    print(f"清洗后的数据已保存到 {cleaned_file}")
except Exception as e:
    print(f"保存清洗后的数据失败: {e}")

# 在分割数据后立即应用改进的预处理
X_train_proc, X_val_proc, X_test_proc = preprocess_features(X_train, X_val, X_test, random_seed)

# 更新后续代码中使用的变量
X_train = X_train_proc
X_val = X_val_proc 
X_test = X_test_proc

results = {}
print("\n========== 初始数据检查 ==========")
print(f"X_train 形状: {X_train.shape}, 包含NaN: {X_train.isna().any().any()}")
print(f"y_train 形状: {y_train.shape}, 包含NaN: {y_train.isna().any()}")
print(f"标签分布: {y_train.value_counts()}")
print(f"X_train 列数据类型:\n{X_train.dtypes}")
print("===================================")

# 4. 模型训练与10-fold交叉验证
print("\n【使用更真实的评估方法】")
print("为使评估更真实，我们将:")
print("1. 使用独立的测试集和严格的交叉验证")
print("2. 报告多种评估指标而非单一指标")
print("3. 与多种基线模型比较")
print("4. 防止可能的特征泄露")

# 调整模型复杂度，为不同模型使用不同的随机种子，但整体降低复杂度
models = {
    'ANN (神经网络)': MLPClassifier(hidden_layer_sizes=(15,), max_iter=150, alpha=0.2, random_state=random_seed),
    'Naive Bayes (朴素贝叶斯)': GaussianNB(),
    'Decision Tree (决策树)': DecisionTreeClassifier(max_depth=3, min_samples_split=30, min_samples_leaf=15, random_state=random_seed+10),
    'Random Forest (随机森林)': RandomForestClassifier(n_estimators=20, max_depth=4, min_samples_split=30, min_samples_leaf=15, random_state=random_seed+20)
}

# 记录最佳模型
best_model = None
best_score = 0

# 使用交叉验证评估每个模型
for name, model in models.items():
    print(f"\n评估模型: {name}")
    try:
        # 创建交叉验证对象，用于模型评估
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=random_seed + 30)
        # 处理可能的无穷值和NaN
        X_train_clean = X_train.replace([np.inf, -np.inf], np.nan).fillna(0)
        X_val_clean = X_val.replace([np.inf, -np.inf], np.nan).fillna(0)
        X_test_clean = X_test.replace([np.inf, -np.inf], np.nan).fillna(0)
        y_train_clean = y_train.astype(int)
        y_val_clean = y_val.astype(int)
        y_test_clean = y_test.astype(int)
        
        # 特殊处理朴素贝叶斯，因为它不能处理负值
        if name == 'Naive Bayes (朴素贝叶斯)':
            # 确保数据全是非负的
            X_train_min = X_train_clean.min()
            negative_cols = X_train_min[X_train_min < 0].index
            if len(negative_cols) > 0:
                print(f"  注意: 朴素贝叶斯需要非负特征，正在进行转换...")
                for col in negative_cols:
                    shift_value = abs(X_train_min[col]) + 1  # 加1以确保都是正数
                    X_train_clean[col] = X_train_clean[col] + shift_value
                    X_val_clean[col] = X_val_clean[col] + shift_value
                    X_test_clean[col] = X_test_clean[col] + shift_value
        
        # 使用交叉验证评估模型，更真实的评估
        print("  执行5折交叉验证评估...")
        f1_scores = cross_val_score(model, X_train_clean, y_train_clean, cv=cv, scoring='f1')
        acc_scores = cross_val_score(model, X_train_clean, y_train_clean, cv=cv, scoring='accuracy')
        
        # 输出交叉验证结果
        print(f"  交叉验证 F1: {f1_scores.mean():.4f} ± {f1_scores.std():.4f}")
        print(f"  交叉验证 准确率: {acc_scores.mean():.4f} ± {acc_scores.std():.4f}")
        
        # 与基线模型比较
        best_baseline_name = max(baseline_scores.items(), key=lambda x: x[1]['f1_mean'])[0]
        baseline_f1 = baseline_scores[best_baseline_name]['f1_mean']
        f1_improvement = (f1_scores.mean() - baseline_f1) / baseline_f1 * 100 if baseline_f1 > 0 else 0
        print(f"  相比最佳基线({best_baseline_name})的F1提升: {f1_improvement:.2f}%")
        
        # 检查交叉验证结果是否过于乐观
        if f1_scores.mean() > 0.95:
            print("  警告: 交叉验证F1分数异常高(>0.95)，可能存在数据泄露或过拟合!")
        
        # 在整个训练集上训练模型
        print("  在完整训练集上拟合模型...")
        model.fit(X_train_clean, y_train_clean)
        
        # 在验证集上评估
        print("  在验证集上评估...")
        y_val_pred = model.predict(X_val_clean)
        # 检查是否是概率模型
        is_probabilistic = hasattr(model, "predict_proba")
        y_val_pred_proba = None
        if is_probabilistic:
            try:
                y_val_pred_proba = model.predict_proba(X_val_clean)[:, 1]
            except Exception as e:
                print(f"  获取验证集概率预测时出错: {e}")
                is_probabilistic = False
        
        # 计算验证集评估指标
        val_accuracy = accuracy_score(y_val_clean, y_val_pred)
        val_precision = precision_score(y_val_clean, y_val_pred)
        val_recall = recall_score(y_val_clean, y_val_pred)
        val_f1 = f1_score(y_val_clean, y_val_pred)
        val_auc = roc_auc_score(y_val_clean, y_val_pred_proba) if is_probabilistic and y_val_pred_proba is not None else None
        
        # 计算空模型(永远预测多数类)的准确率，作为基线比较
        majority_class = 1 if y_val_clean.mean() >= 0.5 else 0
        baseline_accuracy = max(y_val_clean.mean(), 1 - y_val_clean.mean())
        
        print(f"  验证集 Accuracy: {val_accuracy:.4f} (基线: {baseline_accuracy:.4f})")
        print(f"  验证集 Precision: {val_precision:.4f}")
        print(f"  验证集 Recall: {val_recall:.4f}")
        print(f"  验证集 F1-score: {val_f1:.4f}")
        if val_auc is not None:
            print(f"  验证集 AUC: {val_auc:.4f}")
            
        # 计算模型相对于基线的提升
        accuracy_improvement = (val_accuracy - baseline_accuracy) / baseline_accuracy * 100
        print(f"  相对于基线的准确率提升: {accuracy_improvement:.2f}%")
        
        # 如果准确率接近1，发出警告
        if val_accuracy > 0.95:
            print("  警告: 验证集准确率异常高，可能存在数据泄露或过拟合问题！")
        
        # 最后在最终测试集上评估，每个模型只使用一次
        print("  在最终测试集上评估...")
        y_test_pred = model.predict(X_test_clean)
        y_test_pred_proba = None
        if is_probabilistic:
            try:
                y_test_pred_proba = model.predict_proba(X_test_clean)[:, 1]
            except Exception as e:
                print(f"  获取测试集概率预测时出错: {e}")
        
        # 计算测试集评估指标
        test_accuracy = accuracy_score(y_test_clean, y_test_pred)
        test_precision = precision_score(y_test_clean, y_test_pred)
        test_recall = recall_score(y_test_clean, y_test_pred)
        test_f1 = f1_score(y_test_clean, y_test_pred)
        test_auc = roc_auc_score(y_test_clean, y_test_pred_proba) if is_probabilistic and y_test_pred_proba is not None else None
        
        # 计算测试集上的基线准确率
        test_baseline = max(y_test_clean.mean(), 1 - y_test_clean.mean())
        test_improvement = (test_accuracy - test_baseline) / test_baseline * 100
        
        print(f"  测试集 Accuracy: {test_accuracy:.4f} (基线: {test_baseline:.4f}, 提升: {test_improvement:.2f}%)")
        print(f"  测试集 Precision: {test_precision:.4f}")
        print(f"  测试集 Recall: {test_recall:.4f}")
        print(f"  测试集 F1-score: {test_f1:.4f}")
        if test_auc is not None:
            print(f"  测试集 AUC: {test_auc:.4f}")
        
        # 保存结果
        results[name] = {
            'cv_f1_mean': f1_scores.mean(),
            'cv_f1_std': f1_scores.std(),
            'cv_acc_mean': acc_scores.mean(),
            'cv_acc_std': acc_scores.std(),
            'f1_improvement': f1_improvement,
            'val_accuracy': val_accuracy,
            'val_precision': val_precision,
            'val_recall': val_recall,
            'val_f1': val_f1,
            'val_auc': val_auc,
            'test_accuracy': test_accuracy,
            'test_precision': test_precision,
            'test_recall': test_recall,
            'test_f1': test_f1,
            'test_auc': test_auc,
            'baseline_accuracy': test_baseline,
            'improvement': test_improvement,
            'model': model,
            'numeric_only': False
        }
        
        # 更新最佳模型 - 使用交叉验证F1分数而非单一验证集分数
        if f1_scores.mean() > best_score:
            best_score = f1_scores.mean()
            best_model = name
            print(f"  更新最佳模型为: {name}, CV F1分数: {f1_scores.mean():.4f}")
    
    except Exception as e:
        print(f"  警告: 模型 {name} 训练/评估失败: {e}")
        print("  错误详细信息:")
        traceback.print_exc()
        print("  可能是因为模型不支持非数值特征，尝试进行数值转换...")
        
        try:
            # 创建训练和测试数据的副本，仅保留数值型列
            X_train_numeric = X_train.select_dtypes(include=[np.number])
            X_val_numeric = X_val.select_dtypes(include=[np.number])
            X_test_numeric = X_test.select_dtypes(include=[np.number])
            
            print(f"  使用 {X_train_numeric.shape[1]} 个数值特征训练模型")
            
            # 处理无穷值和NaN
            X_train_numeric = X_train_numeric.replace([np.inf, -np.inf], np.nan).fillna(0)
            X_val_numeric = X_val_numeric.replace([np.inf, -np.inf], np.nan).fillna(0) 
            X_test_numeric = X_test_numeric.replace([np.inf, -np.inf], np.nan).fillna(0)
            
            # 确保目标变量是整数类型
            y_train_clean = y_train.astype(int)
            y_val_clean = y_val.astype(int)
            y_test_clean = y_test.astype(int)
            
            # 特殊处理朴素贝叶斯
            if name == 'Naive Bayes (朴素贝叶斯)':
                # 确保数据全是非负的
                X_train_min = X_train_numeric.min()
                negative_cols = X_train_min[X_train_min < 0].index
                if len(negative_cols) > 0:
                    print(f"  注意: 朴素贝叶斯需要非负特征，正在进行转换...")
                    for col in negative_cols:
                        shift_value = abs(X_train_min[col]) + 1  # 加1以确保都是正数
                        X_train_numeric[col] = X_train_numeric[col] + shift_value
                        X_val_numeric[col] = X_val_numeric[col] + shift_value
                        X_test_numeric[col] = X_test_numeric[col] + shift_value
            
            # 使用交叉验证评估模型，更真实的评估
            print("  执行5折交叉验证评估(只用数值特征)...")
            cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=random_seed + 30)
            f1_scores = cross_val_score(model, X_train_numeric, y_train_clean, cv=cv, scoring='f1')
            acc_scores = cross_val_score(model, X_train_numeric, y_train_clean, cv=cv, scoring='accuracy')
            
            # 输出交叉验证结果
            print(f"  [只用数值特征] 交叉验证 F1: {f1_scores.mean():.4f} ± {f1_scores.std():.4f}")
            print(f"  [只用数值特征] 交叉验证 准确率: {acc_scores.mean():.4f} ± {acc_scores.std():.4f}")
            # 与基线模型比较
            best_baseline_name = max(baseline_scores.items(), key=lambda x: x[1]['f1_mean'])[0]
            baseline_f1 = baseline_scores[best_baseline_name]['f1_mean']
            f1_improvement = (f1_scores.mean() - baseline_f1) / baseline_f1 * 100 if baseline_f1 > 0 else 0
            print(f"  [只用数值特征] 相比最佳基线({best_baseline_name})的F1提升: {f1_improvement:.2f}%")
            
            # 检查交叉验证结果是否过于乐观
            if f1_scores.mean() > 0.95:
                print("  警告: 交叉验证F1分数异常高(>0.95)，可能存在数据泄露或过拟合!")
            
            # 在整个训练集上训练模型
            print("  在完整训练集上拟合模型(只用数值特征)...")
            model.fit(X_train_numeric, y_train_clean)
            
            # 在验证集上评估
            print("  在验证集上评估...")
            y_val_pred = model.predict(X_val_numeric)
            is_probabilistic = hasattr(model, "predict_proba")
            y_val_pred_proba = None
            if is_probabilistic:
                try:
                    y_val_pred_proba = model.predict_proba(X_val_numeric)[:, 1]
                except Exception as e:
                    print(f"  获取验证集概率预测时出错: {e}")
                    is_probabilistic = False
            
            # 计算验证集评估指标
            val_accuracy = accuracy_score(y_val_clean, y_val_pred)
            val_precision = precision_score(y_val_clean, y_val_pred)
            val_recall = recall_score(y_val_clean, y_val_pred)
            val_f1 = f1_score(y_val_clean, y_val_pred)
            val_auc = roc_auc_score(y_val_clean, y_val_pred_proba) if is_probabilistic and y_val_pred_proba is not None else None
            
            # 计算空模型(永远预测多数类)的准确率，作为基线比较
            majority_class = 1 if y_val_clean.mean() >= 0.5 else 0
            baseline_accuracy = max(y_val_clean.mean(), 1 - y_val_clean.mean())
            
            print(f"  [只用数值特征] 验证集 Accuracy: {val_accuracy:.4f} (基线: {baseline_accuracy:.4f})")
            print(f"  [只用数值特征] 验证集 Precision: {val_precision:.4f}")
            print(f"  [只用数值特征] 验证集 Recall: {val_recall:.4f}")
            print(f"  [只用数值特征] 验证集 F1-score: {val_f1:.4f}")
            if val_auc is not None:
                print(f"  [只用数值特征] 验证集 AUC: {val_auc:.4f}")
            
            # 计算模型相对于基线的提升
            accuracy_improvement = (val_accuracy - baseline_accuracy) / baseline_accuracy * 100
            print(f"  [只用数值特征] 相对于基线的准确率提升: {accuracy_improvement:.2f}%")
            
            # 在测试集上评估最终性能
            print("  在最终测试集上评估(只用数值特征)...")
            y_test_pred = model.predict(X_test_numeric)
            y_test_pred_proba = None
            if is_probabilistic:
                try:
                    y_test_pred_proba = model.predict_proba(X_test_numeric)[:, 1]
                except Exception as e:
                    print(f"  获取测试集概率预测时出错: {e}")
            
            # 计算测试集评估指标
            test_accuracy = accuracy_score(y_test_clean, y_test_pred)
            test_precision = precision_score(y_test_clean, y_test_pred)
            test_recall = recall_score(y_test_clean, y_test_pred)
            test_f1 = f1_score(y_test_clean, y_test_pred)
            test_auc = roc_auc_score(y_test_clean, y_test_pred_proba) if is_probabilistic and y_test_pred_proba is not None else None
            
            # 计算测试集上的基线准确率
            test_baseline = max(y_test_clean.mean(), 1 - y_test_clean.mean())
            test_improvement = (test_accuracy - test_baseline) / test_baseline * 100
            
            print(f"  [只用数值特征] 测试集 Accuracy: {test_accuracy:.4f} (基线: {test_baseline:.4f})")
            print(f"  [只用数值特征] 测试集 Precision: {test_precision:.4f}")
            print(f"  [只用数值特征] 测试集 Recall: {test_recall:.4f}")
            print(f"  [只用数值特征] 测试集 F1-score: {test_f1:.4f}")
            if test_auc is not None:
                print(f"  [只用数值特征] 测试集 AUC: {test_auc:.4f}")
            
            # 保存结果
            results[name] = {
                'cv_f1_mean': f1_scores.mean(),
                'cv_f1_std': f1_scores.std(),
                'cv_acc_mean': acc_scores.mean(),
                'cv_acc_std': acc_scores.std(),
                'f1_improvement': f1_improvement,
                'val_accuracy': val_accuracy,
                'val_precision': val_precision,
                'val_recall': val_recall,
                'val_f1': val_f1,
                'val_auc': val_auc,
                'test_accuracy': test_accuracy,
                'test_precision': test_precision,
                'test_recall': test_recall,
                'test_f1': test_f1,
                'test_auc': test_auc,
                'baseline_accuracy': test_baseline,
                'improvement': test_improvement,
                'model': model,
                'numeric_only': True  # 标记这个模型只使用了数值特征
            }
            
            # 更新最佳模型 - 使用交叉验证F1分数
            if f1_scores.mean() > best_score:
                best_score = f1_scores.mean()
                best_model = name
                print(f"  更新最佳模型为: {name}, CV F1分数: {f1_scores.mean():.4f}")
        
        except Exception as e2:
            print(f"  模型 {name} 仍然失败: {e2}")
            traceback.print_exc()
            results[name] = {'error': str(e2)}

# 5. 模型比较与可视化
print("\n【模型真实性能比较】")

# 增加性能提升数据
for name, result in results.items():
    if 'error' in result:
        continue
    print(f"\n{name}:")
    if 'cv_f1_mean' in result:
        print(f"交叉验证F1分数: {result['cv_f1_mean']:.4f} ± {result['cv_f1_std']:.4f}")
        print(f"相对基线F1提升: {result['f1_improvement']:.2f}%")
    if 'test_f1' in result:
        print(f"测试集F1分数: {result['test_f1']:.4f}")
        print(f"测试集准确率: {result['test_accuracy']:.4f} (相对基线提升: {result['improvement']:.2f}%)")
        print(f"测试集精确率: {result['test_precision']:.4f}, 召回率: {result['test_recall']:.4f}")
        if result['test_auc'] is not None:
            print(f"测试集AUC: {result['test_auc']:.4f}")

# 添加基线模型到结果中，以便比较
for name, scores in baseline_scores.items():
    results[f"基线: {name}"] = {
        'cv_f1_mean': scores['f1_mean'],
        'cv_f1_std': scores['f1_std'],
        'val_accuracy': scores['val_acc'],
        'val_f1': scores['val_f1'],
        'test_accuracy': None,  # 没有在测试集上评估基线模型
        'test_f1': None,
        'f1_improvement': 0.0,  # 基线相对于自身的提升为0
        'improvement': 0.0
    }

# 比较各个模型的性能
metrics = ['cv_f1_mean', 'f1_improvement', 'val_f1', 'test_f1', 'test_accuracy', 'test_precision', 'test_recall', 'test_auc']
metric_names = {
    'cv_f1_mean': '交叉验证F1',
    'f1_improvement': 'F1提升(%)',
    'val_f1': '验证集F1',
    'test_f1': '测试集F1',
    'test_accuracy': '测试集准确率',
    'test_precision': '测试集精确率',
    'test_recall': '测试集召回率',
    'test_auc': '测试集AUC'
}

# 创建比较数据框
comparison_df = pd.DataFrame(index=list(models.keys()) + [f"基线: {name}" for name in baseline_scores.keys()], 
                            columns=[metric_names[m] for m in metrics])

for model_name in comparison_df.index:
    if model_name in results and 'error' not in results[model_name]:
        for i, metric in enumerate(metrics):
            if metric in results[model_name] and results[model_name][metric] is not None:
                comparison_df.loc[model_name, metric_names[metric]] = results[model_name][metric]

print("\n模型性能比较表:")
print(comparison_df)

# 为了更好的比较，绘制条形图
try:
    log_debug("开始绘制模型性能比较图")
    log_debug(f"比较数据帧内容:\n{comparison_df}")
    
    # 检查数据帧是否为空或包含NaN值
    if comparison_df.empty:
        print("警告: 比较数据帧为空，无法绘制图表")
    else:
        # 填充NaN值以便绘图
        df_plot = comparison_df.copy().fillna(0)
        
        # 绘制交叉验证F1分数比较
        plt.figure(figsize=(14, 6))
        ax = df_plot[['交叉验证F1', 'F1提升(%)']].plot(kind='bar')
        plt.title('模型交叉验证F1分数比较')
        plt.ylabel('分数')
        plt.xlabel('模型')
        plt.xticks(rotation=45)
        plt.grid(True, linestyle='--', alpha=0.5)
        plt.legend(loc='best')
        plt.tight_layout()
        
        # 保存时添加更多信息
        save_path = 'cv_f1_comparison.png'
        plt.savefig(save_path)
        plt.close()
        
        if os.path.exists(save_path):
            print(f"成功保存交叉验证F1比较图到: {save_path}, 文件大小: {os.path.getsize(save_path)} 字节")
        else:
            print(f"警告: 图表似乎未成功保存: {save_path}")
            
        # 绘制测试集指标比较图
        plt.figure(figsize=(14, 6))
        df_plot[['测试集F1', '测试集准确率', '测试集精确率', '测试集召回率']].plot(kind='bar')
        plt.title('模型测试集性能比较')
        plt.ylabel('分数')
        plt.xlabel('模型')
        plt.xticks(rotation=45)
        plt.grid(True, linestyle='--', alpha=0.5)
        plt.legend(loc='best')
        plt.tight_layout()
        
        save_path = 'test_metrics_comparison.png'
        plt.savefig(save_path)
        plt.close()
        
        if os.path.exists(save_path):
            print(f"成功保存测试集指标比较图到: {save_path}, 文件大小: {os.path.getsize(save_path)} 字节")
        else:
            print(f"警告: 图表似乎未成功保存: {save_path}")
            
except Exception as e:
    print(f"绘制模型性能比较图出错: {e}")
    traceback.print_exc()

# 绘制准确率提升对比图
try:
    log_debug("开始绘制准确率提升对比图")
    
    plt.figure(figsize=(12, 6))
    # 绘制相对于基线的提升
    model_names = [name for name in results.keys() if name.startswith("基线:") == False and 'error' not in results[name]]
    improvements = [results[name]['f1_improvement'] for name in model_names]
    
    bars = plt.bar(model_names, improvements, color='green')
    
    # 添加参考线
    plt.axhline(y=0, color='r', linestyle='--', label='基线(永远预测多数类)')
    
    # 添加数据标签
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 1,
                f"{height:.1f}%", ha='center', va='bottom', rotation=0)
    
    plt.title('各模型相对于基线的F1提升')
    plt.ylabel('提升百分比(%)')
    plt.xlabel('模型')
    plt.xticks(rotation=45)
    plt.legend(loc='best')
    plt.grid(True, linestyle='--', alpha=0.5)
    plt.tight_layout()
    
    save_path = 'f1_improvement.png'
    plt.savefig(save_path)
    plt.close()
    
    if os.path.exists(save_path):
        print(f"成功保存F1提升对比图到: {save_path}, 文件大小: {os.path.getsize(save_path)} 字节")
    else:
        print(f"警告: F1提升对比图似乎未成功保存: {save_path}")
except Exception as e:
    print(f"绘制准确率提升对比图出错: {e}")
    traceback.print_exc()

print(f"\n最佳模型: {best_model}, CV F1-score: {best_score:.4f}")
print("(注意: 随机种子的变化可能导致结果在每次运行时略有不同)")

# 绘制ROC曲线
print("\n绘制ROC曲线和混淆矩阵...")
try:
    log_debug("开始绘制ROC曲线")
    plt.figure(figsize=(10, 8))

    has_valid_curve = False
    for name, result in results.items():
        if 'error' in result or 'test_auc' not in result or result['test_auc'] is None:
            log_debug(f"模型 {name} 没有有效的AUC值，跳过")
            continue
        
        # 检查模型是否只使用了数值特征
        if 'numeric_only' in result and result['numeric_only']:
            X_test_data = X_test.select_dtypes(include=[np.number])
            log_debug(f"模型 {name} 只使用数值特征，特征数: {X_test_data.shape[1]}")
        else:
            X_test_data = X_test
        
        model = result['model']
        try:
            y_pred_proba = model.predict_proba(X_test_data)[:, 1]
            fpr, tpr, _ = roc_curve(y_test, y_pred_proba)
            roc_auc = auc(fpr, tpr)
            plt.plot(fpr, tpr, lw=2, label=f'{name} (AUC = {roc_auc:.4f})')
            has_valid_curve = True
            log_debug(f"成功为模型 {name} 绘制ROC曲线, AUC = {roc_auc:.4f}")
        except Exception as e:
            print(f"无法为模型 {name} 绘制ROC曲线: {e}")
    
    if has_valid_curve:
        # 添加随机猜测的基线
        plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--', label='随机猜测')
        plt.xlim([0.0, 1.0])
        plt.ylim([0.0, 1.05])
        plt.xlabel('假正例率 (FPR)')
        plt.ylabel('真正例率 (TPR)')
        plt.title('各模型ROC曲线对比')
        plt.legend(loc="lower right")
        plt.grid(True, linestyle='--', alpha=0.6)
        plt.tight_layout()
        
        save_path = 'roc_curve_comparison.png'
        plt.savefig(save_path)
        plt.close()
        
        if os.path.exists(save_path):
            print(f"成功保存ROC曲线到: {save_path}, 文件大小: {os.path.getsize(save_path)} 字节")
        else:
            print(f"警告: ROC曲线似乎未成功保存: {save_path}")
    else:
        print("警告: 没有找到任何有效的概率预测模型，无法绘制ROC曲线")
except Exception as e:
    print(f"绘制ROC曲线总体出错: {e}")
    traceback.print_exc()

# 混淆矩阵
try:
    log_debug("开始绘制混淆矩阵")
    best_model_name = best_model
    if best_model_name in results and 'model' in results[best_model_name]:
        best_model_obj = results[best_model_name]['model']
        log_debug(f"找到最佳模型: {best_model_name}")
        
        # 检查最佳模型是否只使用了数值特征
        if 'numeric_only' in results[best_model_name] and results[best_model_name]['numeric_only']:
            best_X_test = X_test.select_dtypes(include=[np.number])
            log_debug(f"最佳模型只使用数值特征, 特征数: {best_X_test.shape[1]}")
        else:
            best_X_test = X_test
        
        try:
            y_pred = best_model_obj.predict(best_X_test)
            
            # 获取正标签和负标签的实际名称
            print("\n最佳模型分类报告:")
            classification_report_text = classification_report(y_test, y_pred)
            print(classification_report_text)
            
            # 创建并可视化混淆矩阵
            cm = confusion_matrix(y_test, y_pred)
            log_debug(f"混淆矩阵:\n{cm}")
            
            plt.figure(figsize=(8, 6))
            
            # 计算百分比
            cm_normalized = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
            
            # 创建带有百分比的标注
            annot = np.empty_like(cm, dtype='<U10')
            nrows, ncols = cm.shape
            for i in range(nrows):
                for j in range(ncols):
                    annot[i, j] = f'{cm[i, j]}\n{cm_normalized[i, j]*100:.1f}%'
            
            # 绘制热图
            sns.heatmap(cm, annot=annot, fmt='', cmap='Blues', 
                        xticklabels=['正常', '异常'], 
                        yticklabels=['正常', '异常'])
            plt.title(f'最佳模型 ({best_model_name}) 混淆矩阵')
            plt.xlabel('预测标签')
            plt.ylabel('真实标签')
            plt.tight_layout()
            
            save_path = 'confusion_matrix.png'
            plt.savefig(save_path)
            plt.close()
            
            if os.path.exists(save_path):
                print(f"成功保存混淆矩阵到: {save_path}, 文件大小: {os.path.getsize(save_path)} 字节")
            else:
                print(f"警告: 混淆矩阵似乎未成功保存: {save_path}")
            
            # 绘制特征重要性
            if best_model_name in ['Random Forest (随机森林)', 'Decision Tree (决策树)']:
                try:
                    log_debug("开始计算特征重要性...")
                    feature_importances = best_model_obj.feature_importances_
                    
                    # 获取用于训练的特征名
                    if 'numeric_only' in results[best_model_name] and results[best_model_name]['numeric_only']:
                        feature_names = X_test.select_dtypes(include=[np.number]).columns
                    else:
                        feature_names = X_test.columns
                    
                    # 创建特征重要性表
                    importance_df = pd.DataFrame({
                        'Feature': feature_names,
                        'Importance': feature_importances
                    }).sort_values('Importance', ascending=False)
                    
                    print("\n前10个最重要特征:")
                    print(importance_df.head(10))
                    
                    # 绘制特征重要性
                    plt.figure(figsize=(12, 6))
                    top_features = importance_df.head(min(15, len(importance_df)))
                    sns.barplot(x='Importance', y='Feature', data=top_features)
                    plt.title('特征重要性排名')
                    plt.tight_layout()
                    
                    save_path = 'feature_importance.png'
                    plt.savefig(save_path)
                    plt.close()
                    
                    if os.path.exists(save_path):
                        print(f"成功保存特征重要性图到: {save_path}, 文件大小: {os.path.getsize(save_path)} 字节")
                    else:
                        print(f"警告: 特征重要性图似乎未成功保存: {save_path}")
                except Exception as e:
                    print(f"生成特征重要性图出错: {e}")
                    traceback.print_exc()
        except Exception as e:
            print(f"生成混淆矩阵出错: {e}")
            traceback.print_exc()
    else:
        print("无法找到有效的最佳模型进行可视化")
except Exception as e:
    print(f"混淆矩阵总体处理出错: {e}")
    traceback.print_exc()

# 保存处理后的数据和结果
try:
    # 保存特征和标签
    features_file = 'features_with_labels.csv'
    X_df = X.copy()
    X_df['anomaly_label'] = y
    X_df.to_csv(features_file, index=False, encoding='utf-8')
    
    # 保存模型比较结果
    comparison_file = 'model_comparison.csv'
    comparison_df.to_csv(comparison_file, encoding='utf-8')
    
    print("\n已保存数据文件:")
    if os.path.exists(features_file):
        print(f"1. {features_file}: 特征和标签 ({os.path.getsize(features_file)} 字节)")
    if os.path.exists(comparison_file):
        print(f"2. {comparison_file}: 模型比较结果 ({os.path.getsize(comparison_file)} 字节)")
except Exception as e:
    print(f"保存数据失败: {e}")
    traceback.print_exc()

print("\n网络异常检测任务已完成!")
print("输出文件:")
print("1. cleaned_network_data.csv: 清洗后的数据集")
print("2. features_with_labels.csv: 提取的特征和异常标签")
print("3. model_comparison.csv: 模型性能比较")
print("4. roc_curve_comparison.png: ROC曲线对比图")
print("5. confusion_matrix.png: 最佳模型混淆矩阵")
print("6. feature_importance.png: 特征重要性(如果最佳模型支持)")

# 应用特征选择来移除不相关特征
print("\n【添加特征选择步骤】")
from sklearn.feature_selection import SelectKBest, mutual_info_classif, VarianceThreshold

# 首先确保只使用数值型特征，避免字符串转换错误
print("提取数值型特征...")
X_train_numeric = X_train.select_dtypes(include=[np.number])
X_val_numeric = X_val.select_dtypes(include=[np.number])
X_test_numeric = X_test.select_dtypes(include=[np.number])
print(f"识别到 {X_train_numeric.shape[1]}/{X_train.shape[1]} 个数值型特征")

if X_train_numeric.shape[1] > 0:  # 确保有数值型特征可供处理
    # 首先移除方差极低的特征（几乎是常数）
    print("移除低方差特征...")
    var_selector = VarianceThreshold(threshold=0.01)  # 方差阈值设为0.01
    X_train_var = var_selector.fit_transform(X_train_numeric)
    X_val_var = var_selector.transform(X_val_numeric)
    X_test_var = var_selector.transform(X_test_numeric)

    # 获取保留的特征列
    remaining_features = [X_train_numeric.columns[i] for i in range(X_train_numeric.shape[1]) 
                        if var_selector.get_support()[i]]
    print(f"移除低方差特征后，保留 {len(remaining_features)}/{X_train_numeric.shape[1]} 个特征")

    # 创建特征列索引的DataFrame
    X_train_numeric = pd.DataFrame(X_train_var, columns=remaining_features)
    X_val_numeric = pd.DataFrame(X_val_var, columns=remaining_features)
    X_test_numeric = pd.DataFrame(X_test_var, columns=remaining_features)

    # 使用互信息方法选择信息量最大的特征
    print("基于互信息选择最相关特征...")
    k_best = min(40, len(remaining_features))  # 最多选择40个特征或所有特征
    selector = SelectKBest(mutual_info_classif, k=k_best)
    X_train_selected = selector.fit_transform(X_train_numeric, y_train)
    X_val_selected = selector.transform(X_val_numeric)
    X_test_selected = selector.transform(X_test_numeric)

    # 获取保留的特征列
    selected_features = [remaining_features[i] for i in range(len(remaining_features)) 
                      if selector.get_support()[i]]
    print(f"最终保留 {len(selected_features)}/{len(remaining_features)} 个信息量最大的特征")

    # 将选择的特征重新构建为DataFrame
    X_train_numeric = pd.DataFrame(X_train_selected, columns=selected_features)
    X_val_numeric = pd.DataFrame(X_val_selected, columns=selected_features)
    X_test_numeric = pd.DataFrame(X_test_selected, columns=selected_features)

    # 打印前10个最高信息量的特征
    feature_scores = list(zip(selected_features, selector.scores_))
    feature_scores.sort(key=lambda x: x[1], reverse=True)
    print("前10个最高信息量特征:")
    for feature, score in feature_scores[:min(10, len(feature_scores))]:
        print(f"  - {feature}: {score:.6f}")

    # 更新全局数据集，只使用数值型特征
    X_train = X_train_numeric
    X_val = X_val_numeric
    X_test = X_test_numeric
else:
    print("警告: 没有找到数值型特征，跳过特征选择步骤")

# 检查数据集一致性
print("\n【检查数据集一致性】")
print(f"X_train 形状: {X_train.shape}, 包含NaN: {X_train.isna().any().any()}")
print(f"y_train 形状: {y_train.shape}, 包含NaN: {y_train.isna().any()}")
print(f"标签分布: {y_train.value_counts()}")
print(f"X_train 列数据类型:\n{X_train.dtypes}")
print("===================================")
