import pandas as pd
import matplotlib.pyplot as plt
import matplotlib

matplotlib.rcParams['font.family'] = ['SimHei']  # SimHei 支持中文
matplotlib.rcParams['axes.unicode_minus'] = False  # 正确显示负号

df = pd.read_csv('event.csv', encoding='GB18030')
print(f"数据形状: {df.shape}")

# 统计每列的不同种类数（唯一值数量）
unique_count = df.nunique()
#只保留具有唯一值的列
unique_count = unique_count[unique_count == 1]
print(f"具有唯一值的列: {unique_count.index.tolist()}")
df = df.drop(columns=unique_count.index.tolist())
print(f"数据形状: {df.shape}")
# 绘制每列唯一值数量的柱状图
plt.figure(figsize=(12, 6))
unique_count.plot(kind='bar')
plt.title('单一值的列')
plt.ylabel('种类数')
plt.xlabel('特征')
plt.tight_layout()
plt.savefig('unique_count_bar.png')
plt.close()

print('每个特征的不同种类数柱状图已保存为 unique_count_bar.png')