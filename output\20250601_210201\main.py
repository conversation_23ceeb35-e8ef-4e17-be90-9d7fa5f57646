import pandas as pd
import numpy as np
from sklearn.preprocessing import OrdinalEncoder, StandardScaler, LabelEncoder, OneHotEncoder
from sklearn.model_selection import train_test_split, cross_val_score, RepeatedStratifiedKFold, StratifiedKFold
from sklearn.naive_bayes import GaussianNB
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import RandomForestClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.metrics import classification_report, confusion_matrix, roc_curve, auc, accuracy_score, precision_score, recall_score, f1_score
from sklearn.feature_selection import SelectKBest, f_classif, mutual_info_classif
from sklearn.utils import resample
import matplotlib.pyplot as plt
import seaborn as sns
import joblib
import os
import matplotlib
import warnings
import time
import random
import shutil
import datetime
from sklearn.impute import KNNImputer

# 设置随机种子，增加随机性
random_seed = random.randint(0, 100)
np.random.seed(random_seed)

warnings.filterwarnings('ignore')

# 设置中文字体（适配 Windows / Mac / Linux）
matplotlib.rcParams['font.family'] = ['SimHei']  # SimHei 支持中文
matplotlib.rcParams['axes.unicode_minus'] = False  # 正确显示负号


# ========== 步骤 0：创建带时间戳的输出目录 ==========
now_str = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
output_dir = os.path.join('output', now_str)
os.makedirs(output_dir, exist_ok=True)
print(f"所有输出将保存在: {output_dir}")
# 拷贝当前代码到输出目录
shutil.copy(__file__, os.path.join(output_dir, os.path.basename(__file__)))


# 步骤 1：读取数据
print("\n>>> 步骤 1：读取数据")
df = pd.read_csv('event.csv', encoding='GB18030')
print("数据读取完成，数据形状：", df.shape)


# 步骤 2：清洗数据
print("\n>>> 步骤 2：清洗数据")
# 删除包含 'IP' 或 '时间' 文本的列
cols_to_drop = df.columns[df.columns.str.contains('IP|时间')]
df = df.drop(columns=cols_to_drop)

'''
# KNN填充缺失值（K=7）
if df.isnull().any().any():
    print("使用KNN填充缺失值 (K=7)")
    # 先对非数值型做编码
    for col in df.select_dtypes(include=['object']).columns:
        df[col] = df[col].astype('category').cat.codes
    imputer = KNNImputer(n_neighbors=7)
    df[:] = imputer.fit_transform(df)
else:
    print("无缺失值，无需填充")
'''

# 统计计数列（通常是数值型）随机化
numeric_cols = df.select_dtypes(include=['number']).columns
for col in numeric_cols:
    noise_factor = 0.1
    noise = np.random.normal(0, noise_factor * df[col].std(), size=len(df))
    df[col] = df[col] + noise

# 删除单一值列
unique_counts = df.nunique()
single_unique_columns = unique_counts[unique_counts==1].index
if len(single_unique_columns) > 0:
    df = df.drop(columns=single_unique_columns)
print("数据形状:", df.shape)

# 步骤 3：添加标签列 - 修改标签生成逻辑，使其更具挑战性
print("\n>>> 步骤 3：添加标签列")
def multi_class_label(row):
    # 修改标签生成逻辑，增加随机性和复杂性
    # 检查必要的列是否存在
    
    for col in required_cols:
        if col not in row:
            print(f"警告: 缺少必要的列 '{col}'")
            return np.random.choice([0, 1], p=[0.7, 0.3])  # 增加随机性
    
    # 1. 检查"事件类型"是否包含"网络攻击"或"信息刺探"
    if isinstance(row['事件类型'], str) and ('攻击' in row['事件类型'] or '刺探' in row['事件类型']):
        return 1 if np.random.random() > 0.05 else 0  # 95%概率返回1
    
    # 2. 检查"事件等级"是否为"严重"或"重要"
    if row['事件等级'] in ['严重', '重要']:
        return 1 if np.random.random() > 0.05 else 0  # 80%概率返回1
    
    # 3. 检查"结果"是否为"成功"
    if row['结果'] == '成功':
        return 1 if np.random.random() > 0.05 else 0  # 70%概率返回1
    
    # 4. 随机因素
    if np.random.random() < 0.00:  # 5%的随机标记为异常
        return 1
    
    
    # 如果以上条件都不满足，则标记为正常
    return 0

# 确保必要的列存在
required_cols = ['事件类型', '事件等级', '结果']
if all(col in df.columns for col in required_cols):
    df['is_anomaly'] = df.apply(multi_class_label, axis=1)
    print(f"标签分布: 0: {sum(df['is_anomaly'] == 0)}, 1: {sum(df['is_anomaly'] == 1)}")
else:
    missing_cols = [col for col in required_cols if col not in df.columns]
    print(f"错误: 缺少必要的列: {missing_cols}")
    raise ValueError(f"缺少必要的列: {missing_cols}")

# 步骤 4：划分特征与标签
print("\n>>> 步骤 4：划分特征与标签")
drop_columns = ['is_anomaly'] 
# 删除用于生成标签的列（如果存在）
for col in ['事件类型', '事件等级', '结果', '备用字符串1']:
    if col in df.columns:
        drop_columns.append(col)

X = df.drop(columns=drop_columns)
y = df['is_anomaly']

# 打印不平衡比例
anomaly_ratio = sum(y == 1) / len(y)
print(f"异常样本比例: {anomaly_ratio:.2%}")

# 保留原始列名用于输出
original_columns = X.columns.tolist()

# 步骤 5：平衡数据集
print("\n>>> 步骤 5：平衡数据集")
# 将数据分成正常和异常样本
df_normal = df[df['is_anomaly'] == 0]
df_anomaly = df[df['is_anomaly'] == 1]

# 获取较少类别的样本数
min_samples = min(len(df_normal), len(df_anomaly))
max_samples = max(len(df_normal), len(df_anomaly))

# 下采样较多的类别
if len(df_normal) > len(df_anomaly):
    print(f"下采样正常样本从 {len(df_normal)} 到 {min_samples}")
    df_normal = resample(df_normal, replace=False, n_samples=min_samples, random_state=random_seed)
else:
    print(f"下采样异常样本从 {len(df_anomaly)} 到 {min_samples}")
    df_anomaly = resample(df_anomaly, replace=False, n_samples=min_samples, random_state=random_seed)

# 合并下采样后的数据
df_balanced = pd.concat([df_normal, df_anomaly])

# 重新分配特征和标签
X = df_balanced.drop(columns=drop_columns)
y = df_balanced['is_anomaly']

print(f"平衡后的数据集形状: {X.shape}, 标签分布: 0: {sum(y == 0)}, 1: {sum(y == 1)}")

# 步骤 6：划分训练测试集
print("\n>>> 步骤 6：划分训练测试集")
X_train_raw, X_test_raw, y_train, y_test = train_test_split(X, y, test_size=1/3, stratify=y, random_state=random_seed)

# 步骤 7：处理缺失值
print("\n>>> 步骤 7：处理缺失值")
# 基于训练集删除高缺失率列
missing_persentage = X_train_raw.isnull().mean()
columns_to_drop = missing_persentage[missing_persentage > 0.3].index
if len(columns_to_drop) > 0:
    X_train_raw = X_train_raw.drop(columns=columns_to_drop)
    X_test_raw = X_test_raw.drop(columns=columns_to_drop)
    print(f"删除了 {len(columns_to_drop)} 个高缺失率列")

# 填充数值列（用训练集均值）
numeric_columns = X_train_raw.select_dtypes(include=['number']).columns
for col in numeric_columns:
    train_mean = X_train_raw[col].mean()
    X_train_raw[col].fillna(train_mean, inplace=True)
    X_test_raw[col].fillna(train_mean, inplace=True)

# 填充非数值列（用'UNKNOWN'）
non_numeric_columns = X_train_raw.select_dtypes(exclude=['number']).columns
for col in non_numeric_columns:
    X_train_raw[col].fillna('UNKNOWN', inplace=True)
    X_test_raw[col].fillna('UNKNOWN', inplace=True)

print("处理缺失值完成，训练集形状:", X_train_raw.shape, "测试集形状:", X_test_raw.shape)

# 步骤 8：特征编码
print("\n>>> 步骤 8：特征编码")
X_train = X_train_raw.copy()
X_test = X_test_raw.copy()
categorical_cols = X_train.select_dtypes(include=['object']).columns.tolist()
numeric_cols = X_train.select_dtypes(include=['number']).columns.tolist()

print("分类特征列：", categorical_cols)

# 为每一列建立一个 LabelEncoder 实例
label_encoders = {}
for col in categorical_cols:
    le = LabelEncoder()
    # 确保数据类型统一
    X_train[col] = X_train[col].astype(str)
    X_test[col] = X_test[col].astype(str)
    
    combined = pd.concat([X_train[col], X_test[col]]).astype(str)
    le.fit(combined)
    X_train[col] = le.transform(X_train[col])
    X_test[col] = le.transform(X_test[col])
    label_encoders[col] = le

print("使用 LabelEncoder 完成分类特征编码")

# 确保数据是数值型的
for col in X_train.columns:
    if X_train[col].dtype == 'object':
        print(f"警告: 列 '{col}' 仍然是对象类型，尝试转换为数值")
        X_train[col] = pd.to_numeric(X_train[col], errors='coerce').fillna(0)
        X_test[col] = pd.to_numeric(X_test[col], errors='coerce').fillna(0)

# 步骤 9：特征标准化
print("\n>>> 步骤 9：特征标准化")
# 先检查是否有无限值或NaN
has_inf = np.any(np.isinf(X_train.values)) or np.any(np.isinf(X_test.values))
has_nan = X_train.isna().any().any() or X_test.isna().any().any()

if has_inf or has_nan:
    print("警告: 数据中存在无限值或NaN，尝试修复")
    X_train = X_train.replace([np.inf, -np.inf], np.nan).fillna(0)
    X_test = X_test.replace([np.inf, -np.inf], np.nan).fillna(0)

scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

# 步骤 10：特征选择
print("\n>>> 步骤 10：特征选择")
# 确保特征数大于1
if X_train.shape[1] <= 1:
    print("警告: 特征数量不足，跳过特征选择")
    X_train_selected = X_train_scaled
    X_test_selected = X_test_scaled
    selector_mask = np.ones(X_train.shape[1], dtype=bool)
else:
    # 计算每个特征的方差
    feature_variance = np.var(X_train_scaled, axis=0)
    # 仅选择方差不为零的特征
    non_zero_var_indices = feature_variance != 0
    
    if sum(non_zero_var_indices) == 0:
        print("警告: 所有特征方差为零，跳过特征选择")
        X_train_selected = X_train_scaled
        X_test_selected = X_test_scaled
        selector_mask = np.ones(X_train.shape[1], dtype=bool)
    else:
        X_train_scaled_filtered = X_train_scaled[:, non_zero_var_indices]
        X_test_scaled_filtered = X_test_scaled[:, non_zero_var_indices]
        
        # 使用互信息选择特征，减少特征数量
        k = min(min(8, X_train_scaled_filtered.shape[1]), X_train_scaled_filtered.shape[0])
        if k == 0:
            print("警告: 无法确定特征选择的数量，跳过特征选择")
            X_train_selected = X_train_scaled
            X_test_selected = X_test_scaled
            selector_mask = np.ones(X_train.shape[1], dtype=bool)
        else:
            selector = SelectKBest(score_func=mutual_info_classif, k=k)
            X_train_selected = selector.fit_transform(X_train_scaled_filtered, y_train)
            X_test_selected = selector.transform(X_test_scaled_filtered)
            
            # 创建完整的掩码，考虑零方差特征
            selector_mask = np.zeros(X_train.shape[1], dtype=bool)
            selector_mask[non_zero_var_indices] = selector.get_support()

# 打印特征选择信息
original_feature_names = X_train.columns.tolist()
if 'selector_mask' in locals():
    selected_feature_names = [original_feature_names[i] for i in range(len(original_feature_names)) if selector_mask[i]]
    print(f"特征选择: 从 {len(original_feature_names)} 个特征中选择了 {len(selected_feature_names)} 个")
    print("选中的特征:", selected_feature_names)

# 步骤 11：模型训练与评估
print("\n>>> 步骤 11：模型训练与评估")
models = {
    'Naive Bayes': GaussianNB(),
    'Decision Tree': DecisionTreeClassifier(
        max_depth=5,  # 减小树的深度以避免过拟合
        min_samples_split=8,
        min_samples_leaf=10,
        random_state=random_seed
    ),
    'Random Forest': RandomForestClassifier(
        n_estimators=50,  # 减少树的数量
        max_depth=4,  # 减小树的深度
        #min_samples_split=10,
        min_samples_leaf=10,
        random_state=random_seed
    ),

    #'Neural Network': MLPClassifier(
        #hidden_layer_sizes=(10, 5),  # 小型网络
        #max_iter=300,
        #alpha=0.01,  # 增加正则化强度
        #random_state=random_seed
    #)
    
}

results = {}
#cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=random_seed)
cv = RepeatedStratifiedKFold(n_splits=10, n_repeats=10, random_state=42)

for name, model in models.items():        
    print(f"\n>>> 训练模型：{name}")

    try:
        train_start_time = time.time()
        model.fit(X_train_selected, y_train)
        train_end_time = time.time()
        
        # 获取模型预测概率
        if hasattr(model, 'predict_proba'):
            try:
                y_proba = model.predict_proba(X_test_selected)[:, 1]
            except Exception as e:
                print(f"获取预测概率时出错: {e}")
                y_proba = None
        else:
            y_proba = None
        
        # 交叉验证
        try:
            scores = cross_val_score(model, X_train_selected, y_train, cv=cv, scoring='f1')
        except Exception as e:
            print(f"交叉验证时出错: {e}")
            scores = np.array([0])

        test_start_time = time.time()
        y_pred = model.predict(X_test_selected)
        test_end_time = time.time()

        # 计算主要评估指标
        acc = accuracy_score(y_test, y_pred)
        prec = precision_score(y_test, y_pred, zero_division=0)
        rec = recall_score(y_test, y_pred, zero_division=0)
        f1 = f1_score(y_test, y_pred, zero_division=0)

        results[name] = {
            'model': model,
            'f1_cv': np.mean(scores),
            'accuracy': acc,
            'precision': prec,
            'recall': rec,
            'f1': f1,
            'y_pred': y_pred,
            'y_proba': y_proba,
            'train_time': train_end_time-train_start_time,
            'test_time': test_end_time-test_start_time
        }
        
        # 如果有预测概率，则计算AUC
        if y_proba is not None:
            try:
                fpr, tpr, _ = roc_curve(y_test, y_proba)
                results[name]['auc'] = auc(fpr, tpr)
            except Exception as e:
                print(f"计算AUC时出错: {e}")
                results[name]['auc'] = None
        else:
            results[name]['auc'] = None

        print(f"{name} 模型评估指标：")
        print(f"F1 (CV): {results[name]['f1_cv']:.4f}")
        print(f"Accuracy: {results[name]['accuracy']:.4f}")
        print(f"Precision: {results[name]['precision']:.4f}")
        print(f"Recall: {results[name]['recall']:.4f}")
        print(f"F1 (Test): {results[name]['f1']:.4f}")
        print(f"train_time: {results[name]['train_time']:.4f} s")
        print(f"test_time: {results[name]['test_time']:.4f} s")
        if results[name]['auc'] is not None:
            print(f"AUC: {results[name]['auc']:.4f}")
    
    except Exception as e:
        print(f"训练模型 {name} 时出错: {e}")
        continue

# 如果没有成功训练任何模型，则结束程序
if not results:
    print("错误: 没有成功训练的模型")
    exit(1)

# 步骤 12：可视化 ROC
print("\n>>> 步骤 12：可视化 ROC")
os.makedirs('output', exist_ok=True)
try:
    plt.figure(figsize=(10, 6))
    has_roc = False
    for name, res in results.items():
        if res['y_proba'] is not None and res['auc'] is not None:
            fpr, tpr, _ = roc_curve(y_test, res['y_proba'])
            plt.plot(fpr, tpr, label=f"{name} (AUC={res['auc']:.2f})")
            has_roc = True
    
    if has_roc:
        plt.plot([0, 1], [0, 1], 'k--')
        plt.xlabel('假阳性率')
        plt.ylabel('真正率')
        plt.title('模型对比：ROC 曲线')
        plt.legend()
        plt.grid()
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'roc_comparison.png'))
        print(f"ROC 曲线图已保存到 {os.path.join(output_dir, 'roc_comparison.png')}")
    else:
        print("警告: 没有足够的数据绘制 ROC 曲线")
except Exception as e:
    print(f"绘制 ROC 曲线时出错: {e}")

# 步骤 13：输出混淆矩阵
print("\n>>> 步骤 13：输出混淆矩阵")
try:
    # 选择最佳模型（基于F1分数）
    valid_models = {k: v for k, v in results.items() if 'f1' in v}
    if valid_models:
        best_model_name = max(valid_models, key=lambda x: valid_models[x]['f1'])
        best_result = results[best_model_name]
        best_model = best_result['model']

        cm = confusion_matrix(y_test, best_result['y_pred'])
        plt.figure(figsize=(6, 4))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
        plt.title(f'混淆矩阵 - 最佳模型：{best_model_name}')
        plt.xlabel('预测值')
        plt.ylabel('真实值')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'confusion_matrix.png'))
        print(f"混淆矩阵已保存到 {os.path.join(output_dir, 'confusion_matrix.png')}")

        # 分类报告
        print(f"\n>>> 最佳模型: {best_model_name}")
        print(classification_report(y_test, best_result['y_pred']))

        # 步骤 14：特征重要性（如果适用）
        if hasattr(best_model, "feature_importances_"):
            try:
                importances = best_model.feature_importances_
                # 获取选定的特征名称
                selected_features = [original_feature_names[i] for i in range(len(original_feature_names)) if selector_mask[i]]
                
                # 确保特征重要性的维度与选定特征数量匹配
                if len(importances) == len(selected_features):
                    indices = np.argsort(importances)[::-1]
                    plt.figure(figsize=(12, 6))
                    selected_features_array = np.array(selected_features)
                    sns.barplot(x=importances[indices], y=selected_features_array[indices])
                    plt.title(f'特征重要性 - {best_model_name}')
                    plt.tight_layout()
                    plt.savefig(os.path.join(output_dir, "feature_importance.png"))
                    print(f"特征重要性图已保存到 {os.path.join(output_dir, 'feature_importance.png')}")
                else:
                    print(f"警告: 特征重要性的维度 ({len(importances)}) 与选定特征数量 ({len(selected_features)}) 不匹配")
            except Exception as e:
                print(f"绘制特征重要性图时出错: {e}")
    else:
        print("警告: 没有有效的模型来确定最佳模型")
except Exception as e:
    print(f"处理混淆矩阵和分类报告时出错: {e}")

# 步骤 15：保存结果
try:
    df.to_csv(os.path.join(output_dir, 'cleaned_event.csv'), index=False, encoding='GB18030')
    
    metrics_list = []
    for name, res in results.items():
        metrics_dict = {
            '模型': name,
            'F1（交叉验证）': res.get('f1_cv', 0),
            '准确率': res.get('accuracy', 0),
            '精确率': res.get('precision', 0),
            '召回率': res.get('recall', 0),
            'F1（测试集）': res.get('f1', 0)
        }
        
        if 'auc' in res and res['auc'] is not None:
            metrics_dict['AUC'] = res['auc']
        else:
            metrics_dict['AUC'] = 0
            
        metrics_dict['训练时间'] = res.get('train_time', 0)
        metrics_dict['测试时间'] = res.get('test_time', 0)
        
        metrics_list.append(metrics_dict)
    
    metrics_df = pd.DataFrame(metrics_list)
    metrics_df.to_csv(os.path.join(output_dir, 'model_metrics.csv'), index=False, encoding='GB18030')
    print(f"\n✅ 实验完成，所有结果已保存到 {output_dir} 文件夹。")
except Exception as e:
    print(f"保存结果时出错: {e}")
