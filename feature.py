import pandas as pd
import numpy as np
from sklearn.impute import SimpleImputer
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import LabelEncoder
import matplotlib.pyplot as plt
import warnings
import matplotlib

warnings.filterwarnings('ignore')
# 设置中文字体（适配 Windows / Mac / Linux）
matplotlib.rcParams['font.family'] = ['SimHei']  # SimHei 支持中文
matplotlib.rcParams['axes.unicode_minus'] = False  # 正确显示负号
# 加载数据
print("加载数据...\n")
data = pd.read_csv('event.csv', encoding='GB18030')

missing_persentage = data.isnull().mean()

print("\n删除缺失值的列")
columns_to_drop = missing_persentage[missing_persentage > 0.0].index
data.drop(columns=columns_to_drop, inplace=True)
print("删除缺失值过多的列后，数据形状：", data.shape)
data.drop(columns=['事件接收时间'])

# 预处理：识别日期列并转换为数值特征
print("处理日期和非数值数据...\n")
for col in data.columns:
    if data[col].dtype == 'object':
        try:
            data[col] = pd.to_datetime(data[col], errors='raise')
            # 提取时间特征
            data[col + '_year'] = data[col].dt.year
            data[col + '_month'] = data[col].dt.month
            data[col + '_day'] = data[col].dt.day
            data[col + '_hour'] = data[col].dt.hour
            data.drop(columns=[col], inplace=True)
        except:
            # 非日期字符串转为类别编码
            data[col] = data[col].astype(str)  # 保证LabelEncoder不出错
            le = LabelEncoder()
            data[col] = le.fit_transform(data[col])

# 将目标变量分离，避免被填充
y = data['事件名称']
X = data.drop(columns=['事件名称'])

# 对数值特征使用均值填充，对非数值特征使用 "unknown" 填充
print("填补缺失值...\n")
# 分离数值特征和非数值特征
numeric_features = X.select_dtypes(include=[np.number]).columns
categorical_features = X.select_dtypes(exclude=[np.number]).columns

# 检查目标变量是否为分类格式
if y.dtype != 'int' and y.dtype != 'float':
    y = LabelEncoder().fit_transform(y.astype(str))

# 模型训练
print("开始训练随机森林模型...\n")
rf_model = RandomForestClassifier(n_estimators=100, random_state=42)
rf_model.fit(X, y)

# 获取特征重要性
importances = rf_model.feature_importances_
feature_names = X.columns
sorted_indices = np.argsort(importances)[::-1]

# 可视化前 N 个重要特征
top_n = 10  # 可修改 N
print(f"正在可视化前 {top_n} 个重要特征...\n")
plt.figure(figsize=(12, 8))
plt.title(f"Top {top_n} 特征重要性", fontsize=16)
plt.bar(range(top_n), importances[sorted_indices[:top_n]], color='skyblue', align='center')
plt.xticks(range(top_n), feature_names[sorted_indices[:top_n]], rotation=90)
plt.xlabel("特征", fontsize=14)
plt.ylabel("重要性", fontsize=14)
plt.tight_layout()
plt.savefig('feature_importance_topN.png', dpi=300)
print("特征重要性图片已生成：feature_importance_topN.png\n")

# 输出前 N 个最重要的特征名称
print("前 N 个最重要的特征：")
for i in range(top_n):
    print(f"{i+1}. {feature_names[sorted_indices[i]]} - 重要性: {importances[sorted_indices[i]]:.4f}")