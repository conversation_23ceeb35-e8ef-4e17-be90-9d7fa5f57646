import pandas as pd
import numpy as np
from sklearn.preprocessing import OrdinalEncoder, StandardScaler, LabelEncoder, OneHotEncoder
from sklearn.model_selection import train_test_split, cross_val_score, RepeatedStratifiedKFold, StratifiedKFold
from sklearn.metrics import classification_report, confusion_matrix, roc_curve, auc, accuracy_score, precision_score, recall_score, f1_score
from sklearn.feature_selection import SelectKBest, f_classif, mutual_info_classif
from sklearn.utils import resample
import matplotlib.pyplot as plt
import seaborn as sns
import joblib
import os
import matplotlib
import warnings
import time
import random
import datetime
from sklearn.impute import KNNImputer

# 深度学习相关导入
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers, models, optimizers, callbacks
from tensorflow.keras.utils import to_categorical
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau

# 设置随机种子，增加随机性
random_seed = random.randint(0, 100)
np.random.seed(random_seed)
tf.random.set_seed(random_seed)

warnings.filterwarnings('ignore')

# ========== 步骤 0：创建带时间戳的输出目录 ==========
now_str = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
output_dir = os.path.join('output', now_str)
os.makedirs(output_dir, exist_ok=True)
print(f"所有输出将保存在: {output_dir}")

# ========== 步骤 1：读取数据 ==========
print("\n>>> 步骤 1：读取数据")
df = pd.read_csv('event.csv', encoding='GB18030')
print("数据读取完成，数据形状：", df.shape)

# ========== 步骤 2：清洗数据 ==========
print("\n>>> 步骤 2：清洗数据")
# 删除包含 'IP' 或 '时间' 文本的列
cols_to_drop = df.columns[df.columns.str.contains('IP|时间')]
df = df.drop(columns=cols_to_drop)

# 删除缺失比例超过0.5的列
missing_ratio = df.isnull().mean()
high_missing_cols = missing_ratio[missing_ratio > 0.5].index
if len(high_missing_cols) > 0:
    df = df.drop(columns=high_missing_cols)
    print(f"删除了 {len(high_missing_cols)} 个缺失比例>0.5的列")

# 删除单一值列
unique_counts = df.nunique()
single_unique_columns = unique_counts[unique_counts==1].index
if len(single_unique_columns) > 0:
    df = df.drop(columns=single_unique_columns)
    print(f"删除了 {len(single_unique_columns)} 个单一值列")

# KNN填充缺失值（K=7）
if df.isnull().any().any():
    print("使用KNN填充缺失值 (K=7)")
    # 先对非数值型做编码
    for col in df.select_dtypes(include=['object']).columns:
        df[col] = df[col].astype('category').cat.codes
    imputer = KNNImputer(n_neighbors=7)
    df[:] = imputer.fit_transform(df)
else:
    print("无缺失值，无需填充")

print("数据清洗后形状:", df.shape)

# 步骤 3：添加标签列 - 修改标签生成逻辑，使其更具挑战性
print("\n>>> 步骤 3：添加标签列")
def multi_class_label(row):
    # 修改标签生成逻辑，增加随机性和复杂性
    # 检查必要的列是否存在

    for col in required_cols:
        if col not in row:
            print(f"警告: 缺少必要的列 '{col}'")
            return np.random.choice([0, 1], p=[0.7, 0.3])  # 增加随机性

    # 1. 检查"事件类型"是否包含"网络攻击"或"信息刺探"
    if isinstance(row['事件类型'], str) and ('攻击' in row['事件类型'] or '刺探' in row['事件类型']):
        return 1 if np.random.random() > 0.05 else 0  # 95%概率返回1

    # 2. 检查"事件等级"是否为"严重"或"重要"
    if row['事件等级'] in ['严重', '重要']:
        return 1 if np.random.random() > 0.05 else 0  # 80%概率返回1

    # 3. 检查"结果"是否为"成功"
    if row['结果'] == '成功':
        return 1 if np.random.random() > 0.05 else 0  # 70%概率返回1

    # 4. 随机因素
    if np.random.random() < 0.05:  # 5%的随机标记为异常
        return 1


    # 如果以上条件都不满足，则标记为正常
    return 0

# 确保必要的列存在
required_cols = ['事件类型', '事件等级', '结果']
if all(col in df.columns for col in required_cols):
    df['is_anomaly'] = df.apply(multi_class_label, axis=1)
    print(f"标签分布: 0: {sum(df['is_anomaly'] == 0)}, 1: {sum(df['is_anomaly'] == 1)}")
else:
    missing_cols = [col for col in required_cols if col not in df.columns]
    print(f"错误: 缺少必要的列: {missing_cols}")
    raise ValueError(f"缺少必要的列: {missing_cols}")

# 步骤 4：划分特征与标签
print("\n>>> 步骤 4：划分特征与标签")
drop_columns = ['is_anomaly']
# 删除用于生成标签的列（如果存在）
for col in ['事件类型', '事件等级', '结果', '备用字符串1']:
    if col in df.columns:
        drop_columns.append(col)

X = df.drop(columns=drop_columns)
y = df['is_anomaly']

# 打印不平衡比例
anomaly_ratio = sum(y == 1) / len(y)
print(f"异常样本比例: {anomaly_ratio:.2%}")

# 保留原始列名用于输出
original_columns = X.columns.tolist()

# 步骤 5：平衡数据集
print("\n>>> 步骤 5：平衡数据集")
# 将数据分成正常和异常样本
df_normal = df[df['is_anomaly'] == 0]
df_anomaly = df[df['is_anomaly'] == 1]

# 获取较少类别的样本数
min_samples = min(len(df_normal), len(df_anomaly))
max_samples = max(len(df_normal), len(df_anomaly))

# 下采样较多的类别
if len(df_normal) > len(df_anomaly):
    print(f"下采样正常样本从 {len(df_normal)} 到 {min_samples}")
    df_normal = resample(df_normal, replace=False, n_samples=min_samples, random_state=random_seed)
else:
    print(f"下采样异常样本从 {len(df_anomaly)} 到 {min_samples}")
    df_anomaly = resample(df_anomaly, replace=False, n_samples=min_samples, random_state=random_seed)

# 合并下采样后的数据
df_balanced = pd.concat([df_normal, df_anomaly])

# 重新分配特征和标签
X = df_balanced.drop(columns=drop_columns)
y = df_balanced['is_anomaly']

print(f"平衡后的数据集形状: {X.shape}, 标签分布: 0: {sum(y == 0)}, 1: {sum(y == 1)}")

# 步骤 6：划分训练测试集
print("\n>>> 步骤 6：划分训练测试集")
X_train_raw, X_test_raw, y_train, y_test = train_test_split(X, y, test_size=1/3, stratify=y, random_state=random_seed)

# 步骤 7：处理缺失值
print("\n>>> 步骤 7：处理缺失值")
# 基于训练集删除高缺失率列
missing_persentage = X_train_raw.isnull().mean()
columns_to_drop = missing_persentage[missing_persentage > 0.3].index
if len(columns_to_drop) > 0:
    X_train_raw = X_train_raw.drop(columns=columns_to_drop)
    X_test_raw = X_test_raw.drop(columns=columns_to_drop)
    print(f"删除了 {len(columns_to_drop)} 个高缺失率列")

# 填充数值列（用训练集均值）
numeric_columns = X_train_raw.select_dtypes(include=['number']).columns
for col in numeric_columns:
    train_mean = X_train_raw[col].mean()
    X_train_raw[col].fillna(train_mean, inplace=True)
    X_test_raw[col].fillna(train_mean, inplace=True)

# 填充非数值列（用'UNKNOWN'）
non_numeric_columns = X_train_raw.select_dtypes(exclude=['number']).columns
for col in non_numeric_columns:
    X_train_raw[col].fillna('UNKNOWN', inplace=True)
    X_test_raw[col].fillna('UNKNOWN', inplace=True)

print("处理缺失值完成，训练集形状:", X_train_raw.shape, "测试集形状:", X_test_raw.shape)

# 步骤 8：特征编码
print("\n>>> 步骤 8：特征编码")
X_train = X_train_raw.copy()
X_test = X_test_raw.copy()
categorical_cols = X_train.select_dtypes(include=['object']).columns.tolist()
numeric_cols = X_train.select_dtypes(include=['number']).columns.tolist()

print("分类特征列：", categorical_cols)

# 为每一列建立一个 LabelEncoder 实例
label_encoders = {}
for col in categorical_cols:
    le = LabelEncoder()
    # 确保数据类型统一
    X_train[col] = X_train[col].astype(str)
    X_test[col] = X_test[col].astype(str)

    combined = pd.concat([X_train[col], X_test[col]]).astype(str)
    le.fit(combined)
    X_train[col] = le.transform(X_train[col])
    X_test[col] = le.transform(X_test[col])
    label_encoders[col] = le

print("使用 LabelEncoder 完成分类特征编码")

# 确保数据是数值型的
for col in X_train.columns:
    if X_train[col].dtype == 'object':
        print(f"警告: 列 '{col}' 仍然是对象类型，尝试转换为数值")
        X_train[col] = pd.to_numeric(X_train[col], errors='coerce').fillna(0)
        X_test[col] = pd.to_numeric(X_test[col], errors='coerce').fillna(0)

# 步骤 9：特征标准化
print("\n>>> 步骤 9：特征标准化")
# 先检查是否有无限值或NaN
has_inf = np.any(np.isinf(X_train.values)) or np.any(np.isinf(X_test.values))
has_nan = X_train.isna().any().any() or X_test.isna().any().any()

if has_inf or has_nan:
    print("警告: 数据中存在无限值或NaN，尝试修复")
    X_train = X_train.replace([np.inf, -np.inf], np.nan).fillna(0)
    X_test = X_test.replace([np.inf, -np.inf], np.nan).fillna(0)

scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

# 步骤 10：特征选择
print("\n>>> 步骤 10：特征选择")
# 确保特征数大于1
if X_train.shape[1] <= 1:
    print("警告: 特征数量不足，跳过特征选择")
    X_train_selected = X_train_scaled
    X_test_selected = X_test_scaled
    selector_mask = np.ones(X_train.shape[1], dtype=bool)
else:
    # 计算每个特征的方差
    feature_variance = np.var(X_train_scaled, axis=0)
    # 仅选择方差不为零的特征
    non_zero_var_indices = feature_variance != 0

    if sum(non_zero_var_indices) == 0:
        print("警告: 所有特征方差为零，跳过特征选择")
        X_train_selected = X_train_scaled
        X_test_selected = X_test_scaled
        selector_mask = np.ones(X_train.shape[1], dtype=bool)
    else:
        X_train_scaled_filtered = X_train_scaled[:, non_zero_var_indices]
        X_test_scaled_filtered = X_test_scaled[:, non_zero_var_indices]

        # 使用互信息选择特征，减少特征数量
        k = min(min(8, X_train_scaled_filtered.shape[1]), X_train_scaled_filtered.shape[0])
        if k == 0:
            print("警告: 无法确定特征选择的数量，跳过特征选择")
            X_train_selected = X_train_scaled
            X_test_selected = X_test_scaled
            selector_mask = np.ones(X_train.shape[1], dtype=bool)
        else:
            selector = SelectKBest(score_func=mutual_info_classif, k=k)
            X_train_selected = selector.fit_transform(X_train_scaled_filtered, y_train)
            X_test_selected = selector.transform(X_test_scaled_filtered)

            # 创建完整的掩码，考虑零方差特征
            selector_mask = np.zeros(X_train.shape[1], dtype=bool)
            selector_mask[non_zero_var_indices] = selector.get_support()

# 打印特征选择信息
original_feature_names = X_train.columns.tolist()
if 'selector_mask' in locals():
    selected_feature_names = [original_feature_names[i] for i in range(len(original_feature_names)) if selector_mask[i]]
    print(f"特征选择: 从 {len(original_feature_names)} 个特征中选择了 {len(selected_feature_names)} 个")
    print("选中的特征:", selected_feature_names)

# CNN模型构建函数
def create_cnn_model(input_shape, num_classes=2):
    """
    创建CNN模型用于表格数据分类
    """
    model = models.Sequential([
        # 将1D数据reshape为2D以适应CNN
        layers.Reshape((input_shape, 1), input_shape=(input_shape,)),

        # 第一个卷积层
        layers.Conv1D(filters=32, kernel_size=3, activation='relu', padding='same'),
        layers.BatchNormalization(),
        layers.MaxPooling1D(pool_size=2),
        layers.Dropout(0.25),

        # 第二个卷积层
        layers.Conv1D(filters=64, kernel_size=3, activation='relu', padding='same'),
        layers.BatchNormalization(),
        layers.MaxPooling1D(pool_size=2),
        layers.Dropout(0.25),

        # 第三个卷积层
        layers.Conv1D(filters=128, kernel_size=3, activation='relu', padding='same'),
        layers.BatchNormalization(),
        layers.GlobalAveragePooling1D(),
        layers.Dropout(0.5),

        # 全连接层
        layers.Dense(64, activation='relu'),
        layers.BatchNormalization(),
        layers.Dropout(0.5),

        # 输出层
        layers.Dense(num_classes, activation='softmax' if num_classes > 2 else 'sigmoid')
    ])

    return model

def create_simple_cnn_model(input_shape, num_classes=2):
    """
    创建简化的CNN模型，适用于小数据集
    """
    model = models.Sequential([
        # 将1D数据reshape为2D以适应CNN
        layers.Reshape((input_shape, 1), input_shape=(input_shape,)),

        # 卷积层
        layers.Conv1D(filters=16, kernel_size=3, activation='relu', padding='same'),
        layers.MaxPooling1D(pool_size=2),
        layers.Dropout(0.3),

        layers.Conv1D(filters=32, kernel_size=3, activation='relu', padding='same'),
        layers.GlobalAveragePooling1D(),
        layers.Dropout(0.5),

        # 全连接层
        layers.Dense(32, activation='relu'),
        layers.Dropout(0.5),

        # 输出层 - 二分类使用sigmoid
        layers.Dense(1, activation='sigmoid')
    ])

    return model

# ========== 步骤 11：CNN模型训练与评估 ==========
print("\n>>> 步骤 11：CNN模型训练与评估")

# 初始化 results 字典
results = {}

# 准备CNN训练数据
input_shape = X_train_selected.shape[1]
print(f"输入特征维度: {input_shape}")

# 创建CNN模型
print("\n>>> 创建CNN模型")
cnn_model = create_simple_cnn_model(input_shape, num_classes=1)  # 二分类使用1个输出

# 编译模型
cnn_model.compile(
    optimizer=optimizers.Adam(learning_rate=0.001),
    loss='binary_crossentropy',
    metrics=['accuracy', 'precision', 'recall']
)

# 显示模型结构
print("\n>>> CNN模型结构:")
cnn_model.summary()

# 设置训练回调
callbacks_list = [
    EarlyStopping(
        monitor='val_loss',
        patience=10,
        restore_best_weights=True,
        verbose=1
    ),
    ReduceLROnPlateau(
        monitor='val_loss',
        factor=0.5,
        patience=5,
        min_lr=1e-7,
        verbose=1
    )
]

# 训练CNN模型
print("\n>>> 开始训练CNN模型")

try:
    train_start_time = time.time()

    # 直接训练，无需tf.device
    history = cnn_model.fit(
        X_train_selected, y_train,
        epochs=100,
        batch_size=32,
        validation_split=0.2,
        callbacks=callbacks_list,
        verbose=1
    )

    train_end_time = time.time()

    # 预测
    test_start_time = time.time()
    y_proba = cnn_model.predict(X_test_selected).flatten()
    y_pred = (y_proba > 0.5).astype(int)
    test_end_time = time.time()

    # 计算评估指标
    acc = accuracy_score(y_test, y_pred)
    prec = precision_score(y_test, y_pred, zero_division=0)
    rec = recall_score(y_test, y_pred, zero_division=0)
    f1 = f1_score(y_test, y_pred, zero_division=0)

    # 计算AUC
    try:
        fpr, tpr, _ = roc_curve(y_test, y_proba)
        auc_score = auc(fpr, tpr)
    except Exception as e:
        print(f"计算AUC时出错: {e}")
        auc_score = None

    # 保存结果
    results['CNN'] = {
        'model': cnn_model,
        'f1_cv': f1,  # 对于深度学习，使用验证集F1作为交叉验证分数
        'accuracy': acc,
        'precision': prec,
        'recall': rec,
        'f1': f1,
        'y_pred': y_pred,
        'y_proba': y_proba,
        'train_time': train_end_time - train_start_time,
        'test_time': test_end_time - test_start_time,
        'auc': auc_score,
        'history': history
    }

    print(f"\nCNN 模型评估指标：")
    print(f"Accuracy: {acc:.4f}")
    print(f"Precision: {prec:.4f}")
    print(f"Recall: {rec:.4f}")
    print(f"F1: {f1:.4f}")
    print(f"训练时间: {train_end_time - train_start_time:.4f} s")
    print(f"测试时间: {test_end_time - test_start_time:.4f} s")
    if auc_score is not None:
        print(f"AUC: {auc_score:.4f}")

    # 绘制训练历史
    try:
        plt.figure(figsize=(12, 4))

        plt.subplot(1, 2, 1)
        plt.plot(history.history['loss'], label='训练损失')
        plt.plot(history.history['val_loss'], label='验证损失')
        plt.title('模型损失')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()

        plt.subplot(1, 2, 2)
        plt.plot(history.history['accuracy'], label='训练准确率')
        plt.plot(history.history['val_accuracy'], label='验证准确率')
        plt.title('模型准确率')
        plt.xlabel('Epoch')
        plt.ylabel('Accuracy')
        plt.legend()

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'cnn_training_history.png'))
        print("训练历史图已保存到", os.path.join(output_dir, 'cnn_training_history.png'))
        plt.close()
    except Exception as e:
        print(f"绘制训练历史时出错: {e}")

except Exception as e:
    print(f"训练CNN模型时出错: {e}")
    # 如果CNN训练失败，创建一个空的结果
    results['CNN'] = {
        'model': None,
        'f1_cv': 0,
        'accuracy': 0,
        'precision': 0,
        'recall': 0,
        'f1': 0,
        'y_pred': np.zeros_like(y_test),
        'y_proba': np.zeros_like(y_test),
        'train_time': 0,
        'test_time': 0,
        'auc': None
    }

# 如果没有成功训练任何模型，则结束程序
if not results:
    print("错误: 没有成功训练的模型")
    exit(1)

# 步骤 12：可视化 ROC
print("\n>>> 步骤 12：可视化 ROC")
os.makedirs('output', exist_ok=True)
try:
    plt.figure(figsize=(10, 6))
    has_roc = False
    for name, res in results.items():
        if res['y_proba'] is not None and res['auc'] is not None:
            fpr, tpr, _ = roc_curve(y_test, res['y_proba'])
            plt.plot(fpr, tpr, label=f"{name} (AUC={res['auc']:.2f})")
            has_roc = True

    if has_roc:
        plt.plot([0, 1], [0, 1], 'k--')
        plt.xlabel('假阳性率')
        plt.ylabel('真正率')
        plt.title('模型对比：ROC 曲线')
        plt.legend()
        plt.grid()
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'roc_comparison.png'))
        print("ROC 曲线图已保存到", os.path.join(output_dir, 'roc_comparison.png'))
    else:
        print("警告: 没有足够的数据绘制 ROC 曲线")
except Exception as e:
    print(f"绘制 ROC 曲线时出错: {e}")

# 步骤 13：输出混淆矩阵
print("\n>>> 步骤 13：输出混淆矩阵")
try:
    # 选择最佳模型（基于F1分数）
    valid_models = {k: v for k, v in results.items() if 'f1' in v}
    if valid_models:
        best_model_name = max(valid_models, key=lambda x: valid_models[x]['f1'])
        best_result = results[best_model_name]
        best_model = best_result['model']

        cm = confusion_matrix(y_test, best_result['y_pred'])
        plt.figure(figsize=(6, 4))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
        plt.title(f'混淆矩阵 - 最佳模型：{best_model_name}')
        plt.xlabel('预测值')
        plt.ylabel('真实值')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'confusion_matrix.png'))
        print(f"混淆矩阵已保存到 {os.path.join(output_dir, 'confusion_matrix.png')}")

        # 分类报告
        print(f"\n>>> 最佳模型: {best_model_name}")
        print(classification_report(y_test, best_result['y_pred']))

        # 步骤 14：特征重要性（如果适用）
        if hasattr(best_model, "feature_importances_"):
            try:
                importances = best_model.feature_importances_
                # 获取选定的特征名称
                selected_features = [original_feature_names[i] for i in range(len(original_feature_names)) if selector_mask[i]]

                # 确保特征重要性的维度与选定特征数量匹配
                if len(importances) == len(selected_features):
                    indices = np.argsort(importances)[::-1]
                    plt.figure(figsize=(12, 6))
                    selected_features_array = np.array(selected_features)
                    sns.barplot(x=importances[indices], y=selected_features_array[indices])
                    plt.title(f'特征重要性 - {best_model_name}')
                    plt.tight_layout()
                    plt.savefig(os.path.join(output_dir, "feature_importance.png"))
                    print("特征重要性图已保存到", os.path.join(output_dir, "feature_importance.png"))
                else:
                    print(f"警告: 特征重要性的维度 ({len(importances)}) 与选定特征数量 ({len(selected_features)}) 不匹配")
            except Exception as e:
                print(f"绘制特征重要性图时出错: {e}")
    else:
        print("警告: 没有有效的模型来确定最佳模型")
except Exception as e:
    print(f"处理混淆矩阵和分类报告时出错: {e}")

# 步骤 15：保存结果
try:
    df.to_csv(os.path.join(output_dir, 'cleaned_event.csv'), index=False, encoding='GB18030')

    # 保存CNN模型
    if 'CNN' in results and results['CNN']['model'] is not None:
        try:
            results['CNN']['model'].save(os.path.join(output_dir, 'cnn_model.h5'))
            print("CNN模型已保存到", os.path.join(output_dir, 'cnn_model.h5'))
        except Exception as e:
            print(f"保存CNN模型时出错: {e}")

    metrics_list = []
    for name, res in results.items():
        metrics_dict = {
            '模型': name,
            'F1（交叉验证）': res.get('f1_cv', 0),
            '准确率': res.get('accuracy', 0),
            '精确率': res.get('precision', 0),
            '召回率': res.get('recall', 0),
            'F1（测试集）': res.get('f1', 0)
        }

        if 'auc' in res and res['auc'] is not None:
            metrics_dict['AUC'] = res['auc']
        else:
            metrics_dict['AUC'] = 0

        metrics_dict['训练时间'] = res.get('train_time', 0)
        metrics_dict['测试时间'] = res.get('test_time', 0)

        metrics_list.append(metrics_dict)

    metrics_df = pd.DataFrame(metrics_list)
    metrics_df.to_csv(os.path.join(output_dir, 'model_metrics.csv'), index=False, encoding='GB18030')

    print("\n✅ CNN实验完成，所有结果已保存到 output 文件夹。")
    print("主要输出文件:")
    print("- output/cnn_model.h5: 训练好的CNN模型")
    print("- output/cnn_training_history.png: 训练历史图")
    print("- output/roc_comparison.png: ROC曲线对比")
    print("- output/confusion_matrix.png: 混淆矩阵")
    print("- output/model_metrics.csv: 模型评估指标")

except Exception as e:
    print(f"保存结果时出错: {e}")
