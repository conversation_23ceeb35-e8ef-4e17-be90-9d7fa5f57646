import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
from sklearn.feature_selection import mutual_info_classif
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import LabelEncoder
import warnings
import matplotlib

warnings.filterwarnings("ignore")

matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体
matplotlib.rcParams['axes.unicode_minus'] = False  # 显示负号
# 创建输出目录
os.makedirs('analysis_output', exist_ok=True)

# 读取数据
print(">>> 读取数据")
df = pd.read_csv('event.csv', encoding='GB18030')
print(f"数据形状: {df.shape}")

# 1. 基本数据探索
print("\n>>> 基本数据探索")
print(f"数据集包含 {df.shape[1]} 个特征")

missing = df.isnull().sum()
missing_cols = missing[missing > 0]
if len(missing_cols) > 0:
    print(f"发现 {len(missing_cols)} 个特征存在缺失值")
else:
    print("数据集无缺失值")

# 2. 分类特征分析
print("\n>>> 分类特征分析")
categorical_cols = df.select_dtypes(include=['object']).columns
print(f"分析 {len(categorical_cols)} 个分类特征")

for col in categorical_cols:
    value_counts = df[col].value_counts()

    # 绘制分布图
    plt.figure(figsize=(10, 6))
    if len(value_counts) > 15:
        # 如果类别太多，只显示前15个
        value_counts.head(15).plot(kind='bar')
        plt.title(f'{col} 前15个值的分布')
    else:
        value_counts.plot(kind='bar')
        plt.title(f'{col} 值分布')
    plt.tight_layout()
    plt.savefig(f'analysis_output/{col}_distribution.png')
    plt.close()

# 3. 数值特征分析
print("\n>>> 数值特征分析")
numeric_cols = df.select_dtypes(include=['number']).columns
print(f"分析 {len(numeric_cols)} 个数值特征")

for col in numeric_cols:
    # 清理数据：移除无穷大值和NaN值
    col_data = df[col].copy()

    # 替换无穷大值为NaN
    col_data = col_data.replace([np.inf, -np.inf], np.nan)

    # 如果所有值都是NaN，跳过这个特征
    if col_data.isna().all():
        print(f"跳过特征 {col}：所有值都是无效的")
        continue

    # 移除NaN值用于绘图
    col_data_clean = col_data.dropna()

    # 如果清理后没有数据，跳过
    if len(col_data_clean) == 0:
        print(f"跳过特征 {col}：清理后无有效数据")
        continue

    # 绘制分布图
    try:
        plt.figure(figsize=(12, 5))
        plt.subplot(1, 2, 1)
        sns.histplot(col_data_clean, kde=True)
        plt.title(f'{col} 分布')

        plt.subplot(1, 2, 2)
        sns.boxplot(y=col_data_clean)
        plt.title(f'{col} 箱线图')

        plt.tight_layout()
        plt.savefig(f'analysis_output/{col}_distribution.png')
        plt.close()
    except Exception as e:
        print(f"绘制特征 {col} 时出错: {e}")
        plt.close()

# 4. 特征相关性分析
print("\n>>> 特征相关性分析")
try:
    # 处理分类特征
    df_encoded = df.copy()

    # 清理数值特征中的无效值
    for col in numeric_cols:
        df_encoded[col] = df_encoded[col].replace([np.inf, -np.inf], np.nan)
        df_encoded[col] = df_encoded[col].fillna(df_encoded[col].median())

    # 编码分类特征
    for col in categorical_cols:
        le = LabelEncoder()
        df_encoded[col] = le.fit_transform(df_encoded[col].astype(str))

    # 计算相关性
    corr_matrix = df_encoded.corr()
    plt.figure(figsize=(14, 12))
    sns.heatmap(corr_matrix, annot=False, cmap='coolwarm', linewidths=0.5)
    plt.title('特征相关性热图')
    plt.tight_layout()
    plt.savefig('analysis_output/correlation_matrix.png')
    plt.close()
    print("相关性分析完成")
except Exception as e:
    print(f"相关性分析出错: {e}")

# 5. 潜在标签特征分析
print("\n>>> 潜在标签特征分析")
def simple_label(row):
    try:
        if '事件类型' in row and isinstance(row['事件类型'], str) and ('攻击' in row['事件类型'] or '刺探' in row['事件类型']):
            return 1
        if '事件等级' in row and row['事件等级'] in ['严重', '重要']:
            return 1
        if '结果' in row and row['结果'] == '成功':
            return 1
        return 0
    except:
        return 0

# 检查必要的列是否存在
required_cols = ['事件类型', '事件等级', '结果']
if all(col in df.columns for col in required_cols):
    try:
        df_encoded['temp_label'] = df.apply(simple_label, axis=1)

        # 计算每个特征与标签的互信息
        X = df_encoded.drop(columns=['temp_label'])
        y = df_encoded['temp_label']

        # 确保X中没有无效值
        X = X.replace([np.inf, -np.inf], np.nan).fillna(0)

        # 计算互信息
        mi_scores = mutual_info_classif(X, y)
        mi_df = pd.DataFrame({'Feature': X.columns, 'MI Score': mi_scores})
        mi_df = mi_df.sort_values('MI Score', ascending=False)

        print(f"计算了 {len(mi_df)} 个特征的互信息分数")

        # 绘制互信息条形图
        plt.figure(figsize=(12, 8))
        sns.barplot(x='MI Score', y='Feature', data=mi_df.head(20))
        plt.title('特征与潜在标签的互信息分数')
        plt.tight_layout()
        plt.savefig('analysis_output/mutual_info_scores.png')
        plt.close()

        # 使用随机森林估计特征重要性
        rf = RandomForestClassifier(n_estimators=100, random_state=42)
        rf.fit(X, y)

        # 特征重要性
        importances = rf.feature_importances_
        indices = np.argsort(importances)[::-1]

        plt.figure(figsize=(12, 8))
        plt.title('特征重要性')
        plt.bar(range(X.shape[1]), importances[indices], align='center')
        plt.xticks(range(X.shape[1]), X.columns[indices], rotation=90)
        plt.tight_layout()
        plt.savefig('analysis_output/feature_importance.png')
        plt.close()

        # 分析标签与关键特征的关系（只分析前3个最重要的特征）
        key_features = mi_df['Feature'].head(3).tolist()
        for feature in key_features:
            try:
                if feature in categorical_cols:
                    # 对于分类特征，分析每个类别的标签分布
                    plt.figure(figsize=(12, 6))
                    sns.countplot(x=feature, hue='temp_label', data=df_encoded)
                    plt.title(f'{feature} 与标签关系')
                    plt.xticks(rotation=45)
                    plt.tight_layout()
                    plt.savefig(f'analysis_output/{feature}_label_relation.png')
                    plt.close()
                else:
                    # 对于数值特征，分析不同标签的分布
                    plt.figure(figsize=(12, 6))
                    sns.histplot(data=df_encoded, x=feature, hue='temp_label', kde=True, element='step')
                    plt.title(f'{feature} 在不同标签下的分布')
                    plt.tight_layout()
                    plt.savefig(f'analysis_output/{feature}_label_distribution.png')
                    plt.close()
            except Exception as e:
                print(f"分析特征 {feature} 时出错: {e}")

        print("标签特征分析完成")
    except Exception as e:
        print(f"标签特征分析出错: {e}")
else:
    print("缺少必要的列，跳过标签特征分析")

print("\n✅ 数据分析完成，结果保存在 analysis_output 文件夹中")